﻿#pragma checksum "..\..\..\..\Views\ModelTrainingView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4EA1FCCD603D3D3CB1EA81331D07B2F4BA745117"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// ModelTrainingView
    /// </summary>
    public partial class ModelTrainingView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DatasetComboBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DatasetInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatasetImageCountText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatasetAnnotationCountText;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatasetSizeText;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatasetClassesText;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ModelArchitectureComboBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider EpochsSlider;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider BatchSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider LearningRateSlider;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartTrainingButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseTrainingButton;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopTrainingButton;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card TrainingProgressCard;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentEpochText;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingLossText;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationLossText;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccuracyText;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OverallProgressBar;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverallProgressText;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar EpochProgressBar;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EpochProgressText;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon TrainingStatusIcon;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrainingStatusText;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Views\ModelTrainingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView TrainedModelsListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/modeltrainingview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ModelTrainingView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DatasetComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 45 "..\..\..\..\Views\ModelTrainingView.xaml"
            this.DatasetComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DatasetComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DatasetInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.DatasetImageCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.DatasetAnnotationCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.DatasetSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DatasetClassesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 70 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseDataset_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 83 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ValidateDataset_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 96 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DataAugmentation_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ModelArchitectureComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.EpochsSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.BatchSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 13:
            this.LearningRateSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 14:
            this.StartTrainingButton = ((System.Windows.Controls.Button)(target));
            
            #line 210 "..\..\..\..\Views\ModelTrainingView.xaml"
            this.StartTrainingButton.Click += new System.Windows.RoutedEventHandler(this.StartTraining_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PauseTrainingButton = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\..\Views\ModelTrainingView.xaml"
            this.PauseTrainingButton.Click += new System.Windows.RoutedEventHandler(this.PauseTraining_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.StopTrainingButton = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\Views\ModelTrainingView.xaml"
            this.StopTrainingButton.Click += new System.Windows.RoutedEventHandler(this.StopTraining_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 253 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewLogs_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TrainingProgressCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 19:
            this.CurrentEpochText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.TrainingLossText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.ValidationLossText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.AccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.OverallProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 24:
            this.OverallProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.EpochProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 26:
            this.EpochProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.TrainingStatusIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 28:
            this.TrainingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.TrainedModelsListView = ((System.Windows.Controls.ListView)(target));
            
            #line 363 "..\..\..\..\Views\ModelTrainingView.xaml"
            this.TrainedModelsListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TrainedModelsListView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 398 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestModel_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 402 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportModel_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 406 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeployModel_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 410 "..\..\..\..\Views\ModelTrainingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteModel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

