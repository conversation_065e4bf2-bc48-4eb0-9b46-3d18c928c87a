apiVersion: v1
kind: ConfigMap
metadata:
  name: medical-config
  namespace: medical-image-analysis
data:
  appsettings.json: |
    {
      "Logging": {
        "LogLevel": {
          "Default": "Information",
          "Microsoft.AspNetCore": "Warning"
        }
      },
      "AllowedHosts": "*",
      "MedicalImageAnalysis": {
        "Python": {
          "Executable": "python"
        },
        "Models": {
          "ModelDirectory": "/app/data/models"
        },
        "TempDirectory": "/app/data/temp",
        "OutputDirectory": "/app/data/output",
        "DICOM": {
          "StoragePath": "/app/data/dicom",
          "MaxFileSize": 500
        },
        "Training": {
          "OutputPath": "/app/data/training",
          "MaxConcurrentJobs": 2
        },
        "Processing": {
          "TempPath": "/app/data/temp",
          "MaxRetries": 3
        }
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: medical-image-analysis
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;

        client_max_body_size 500M;
        client_body_buffer_size 128k;
        client_body_timeout 60s;
        client_header_timeout 60s;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream medical_api {
            server medical-api-service:5000;
            keepalive 32;
        }

        upstream medical_web {
            server medical-web-service:5001;
            keepalive 32;
        }

        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

        server {
            listen 80;
            server_name _;

            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location /api/ {
                limit_req zone=api burst=20 nodelay;
                
                proxy_pass http://medical_api/;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                proxy_connect_timeout 60s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }

            location /api/upload {
                limit_req zone=upload burst=5 nodelay;
                
                proxy_pass http://medical_api/upload;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
                
                client_max_body_size 500M;
                client_body_buffer_size 128k;
            }

            location /hubs/ {
                proxy_pass http://medical_api/hubs/;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                proxy_connect_timeout 7d;
                proxy_send_timeout 7d;
                proxy_read_timeout 7d;
            }

            location / {
                proxy_pass http://medical_web/;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                proxy_connect_timeout 60s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }

            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                proxy_pass http://medical_web;
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header Vary Accept-Encoding;
            }

            error_page 404 /404.html;
            error_page 500 502 503 504 /50x.html;
            
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }
