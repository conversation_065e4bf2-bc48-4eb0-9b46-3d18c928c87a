﻿#pragma checksum "..\..\..\..\Views\DicomUploadView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5DDF346FCD54694F117F6C2E19296F9B1702BB1C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// DicomUploadView
    /// </summary>
    public partial class DicomUploadView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DropZone;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card FileListCard;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView FileListView;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ConfidenceSlider;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card ProcessingCard;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProcessingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\DicomUploadView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessingStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/dicomuploadview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DicomUploadView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DropZone = ((System.Windows.Controls.Border)(target));
            
            #line 41 "..\..\..\..\Views\DicomUploadView.xaml"
            this.DropZone.Drop += new System.Windows.DragEventHandler(this.DropZone_Drop);
            
            #line default
            #line hidden
            
            #line 42 "..\..\..\..\Views\DicomUploadView.xaml"
            this.DropZone.DragOver += new System.Windows.DragEventHandler(this.DropZone_DragOver);
            
            #line default
            #line hidden
            
            #line 43 "..\..\..\..\Views\DicomUploadView.xaml"
            this.DropZone.DragLeave += new System.Windows.DragEventHandler(this.DropZone_DragLeave);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 76 "..\..\..\..\Views\DicomUploadView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectFiles_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FileListCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 4:
            this.FileListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 5:
            
            #line 133 "..\..\..\..\Views\DicomUploadView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearFiles_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 136 "..\..\..\..\Views\DicomUploadView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StartProcessing_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ConfidenceSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 8:
            this.ProcessingCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 9:
            this.ProcessingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 10:
            this.ProcessingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

