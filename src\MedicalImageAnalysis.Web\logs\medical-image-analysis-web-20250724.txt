[2025-07-24 09:06:18.444 +08:00 INF] : 启动医学影像解析系统 Web 应用 {}
[2025-07-24 09:06:18.528 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5002 {"EventId":{"Id":14,"Name":"ListeningOnAddress"}}
[2025-07-24 09:06:18.536 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down. {}
[2025-07-24 09:06:18.538 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Production {}
[2025-07-24 09:06:18.540 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web {}
[2025-07-24 09:07:15.565 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"RequestId":"0HNEA9PMT79BR:********","RequestPath":"/","ConnectionId":"0HNEA9PMT79BR"}
[2025-07-24 09:07:15.597 +08:00 ERR] Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware: An unhandled exception has occurred while executing the request. {"EventId":{"Id":1,"Name":"UnhandledException"},"RequestId":"0HNEA9PMT79BR:********","RequestPath":"/","ConnectionId":"0HNEA9PMT79BR"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
[2025-07-24 09:07:15.617 +08:00 ERR] Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware: An exception was thrown attempting to execute the error handler. {"EventId":{"Id":3,"Name":"Exception"},"RequestId":"0HNEA9PMT79BR:********","RequestPath":"/","ConnectionId":"0HNEA9PMT79BR"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.HandleException(HttpContext context, ExceptionDispatchInfo edi)
[2025-07-24 09:07:15.631 +08:00 ERR] Microsoft.AspNetCore.Server.Kestrel: Connection id "0HNEA9PMT79BR", Request id "0HNEA9PMT79BR:********": An unhandled exception was thrown by the application. {"EventId":{"Id":13,"Name":"ApplicationError"},"RequestId":"0HNEA9PMT79BR:********","RequestPath":"/"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.HandleException(HttpContext context, ExceptionDispatchInfo edi)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
[2025-07-24 09:08:07.661 +08:00 INF] Microsoft.Hosting.Lifetime: Application is shutting down... {}
