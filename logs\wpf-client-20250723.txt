2025-07-23 21:39:18.717 +08:00 [FTL] 应用程序启动失败
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\AI_project\医学影像解析\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at MedicalImageAnalysis.WpfClient.App.OnStartup(StartupEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\App.xaml.cs:line 32
2025-07-23 21:50:14.064 +08:00 [FTL] 应用程序启动失败
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\AI_project\医学影像解析\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at MedicalImageAnalysis.WpfClient.App.OnStartup(StartupEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\App.xaml.cs:line 32
