using FluentAssertions;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Infrastructure.Services;
using MedicalImageAnalysis.Tests.Unit.Common;
using Moq;
using Xunit;

namespace MedicalImageAnalysis.Tests.Unit.Services;

/// <summary>
/// DICOM服务单元测试
/// </summary>
public class DicomServiceTests : DatabaseTestBase
{
    private readonly Mock<ILogger<DicomService>> _mockLogger;
    private readonly DicomService _dicomService;

    public DicomServiceTests()
    {
        _mockLogger = new Mock<ILogger<DicomService>>();
        _dicomService = new DicomService(_mockLogger.Object);
    }

    [Fact]
    public async Task ParseDicomFileAsync_ValidFile_ShouldReturnDicomInfo()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();

        try
        {
            // Act
            var result = await _dicomService.ParseDicomFileAsync(testFilePath);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.DicomInfo.Should().NotBeNull();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ParseDicomFileAsync_InvalidFile_ShouldReturnFailure()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestImageFile(".txt");

        try
        {
            // Act
            var result = await _dicomService.ParseDicomFileAsync(testFilePath);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ParseDicomFileAsync_NonExistentFile_ShouldThrowException()
    {
        // Arrange
        var nonExistentPath = "non_existent_file.dcm";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(
            () => _dicomService.ParseDicomFileAsync(nonExistentPath));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task ParseDicomFileAsync_InvalidPath_ShouldThrowArgumentException(string filePath)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _dicomService.ParseDicomFileAsync(filePath));
    }

    [Fact]
    public async Task ConvertToImageAsync_ValidDicomFile_ShouldReturnImageData()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();

        try
        {
            // Act
            var result = await _dicomService.ConvertToImageAsync(testFilePath);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.ImageData.Should().NotBeNull();
            result.ImageData.Length.Should().BeGreaterThan(0);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ValidateDicomFileAsync_ValidFile_ShouldReturnTrue()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();

        try
        {
            // Act
            var result = await _dicomService.ValidateDicomFileAsync(testFilePath);

            // Assert
            result.Should().BeTrue();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ValidateDicomFileAsync_InvalidFile_ShouldReturnFalse()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestImageFile(".txt");

        try
        {
            // Act
            var result = await _dicomService.ValidateDicomFileAsync(testFilePath);

            // Assert
            result.Should().BeFalse();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ExtractPixelDataAsync_ValidDicomFile_ShouldReturnPixelData()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();

        try
        {
            // Act
            var result = await _dicomService.ExtractPixelDataAsync(testFilePath);

            // Assert
            result.Should().NotBeNull();
            result.Width.Should().BeGreaterThan(0);
            result.Height.Should().BeGreaterThan(0);
            result.Data.Should().NotBeNull();
            result.Data.Length.Should().BeGreaterThan(0);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task GetDicomTagsAsync_ValidFile_ShouldReturnTags()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();

        try
        {
            // Act
            var result = await _dicomService.GetDicomTagsAsync(testFilePath);

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBeEmpty();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task ApplyWindowLevelAsync_ValidParameters_ShouldReturnProcessedData()
    {
        // Arrange
        var pixelData = CreateTestEntity<PixelData>();
        pixelData.Data = Enumerable.Range(0, 1000).Select(i => (double)i).ToArray();
        
        var windowWidth = 400.0;
        var windowCenter = 200.0;

        // Act
        var result = await _dicomService.ApplyWindowLevelAsync(pixelData, windowWidth, windowCenter);

        // Assert
        result.Should().NotBeNull();
        result.Width.Should().Be(pixelData.Width);
        result.Height.Should().Be(pixelData.Height);
        result.Data.Should().NotBeNull();
        result.Data.Length.Should().Be(pixelData.Data.Length);
    }

    [Theory]
    [InlineData(0, 100)]
    [InlineData(-100, 100)]
    [InlineData(100, 0)]
    public async Task ApplyWindowLevelAsync_InvalidParameters_ShouldThrowArgumentException(double windowWidth, double windowCenter)
    {
        // Arrange
        var pixelData = CreateTestEntity<PixelData>();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _dicomService.ApplyWindowLevelAsync(pixelData, windowWidth, windowCenter));
    }

    [Fact]
    public async Task ConvertToJpegAsync_ValidPixelData_ShouldReturnJpegBytes()
    {
        // Arrange
        var pixelData = CreateTestEntity<PixelData>();
        pixelData.Data = Enumerable.Range(0, 1000).Select(i => (double)i).ToArray();
        pixelData.Width = 100;
        pixelData.Height = 10;

        // Act
        var result = await _dicomService.ConvertToJpegAsync(pixelData);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ConvertToPngAsync_ValidPixelData_ShouldReturnPngBytes()
    {
        // Arrange
        var pixelData = CreateTestEntity<PixelData>();
        pixelData.Data = Enumerable.Range(0, 1000).Select(i => (double)i).ToArray();
        pixelData.Width = 100;
        pixelData.Height = 10;

        // Act
        var result = await _dicomService.ConvertToPngAsync(pixelData);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _dicomService.Dispose();
        action.Should().NotThrow();
    }

    [Fact]
    public async Task ProcessMultipleDicomFilesAsync_ValidFiles_ShouldProcessAll()
    {
        // Arrange
        var testFiles = new List<string>
        {
            TestHelpers.CreateTestDicomFile(),
            TestHelpers.CreateTestDicomFile(),
            TestHelpers.CreateTestDicomFile()
        };

        try
        {
            // Act
            var tasks = testFiles.Select(file => _dicomService.ParseDicomFileAsync(file));
            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().HaveCount(3);
            results.Should().OnlyContain(r => r.Success);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFiles.ToArray());
        }
    }

    [Fact]
    public async Task ConcurrentAccess_ShouldBeThreadSafe()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();
        var concurrentTasks = 10;

        try
        {
            // Act
            var tasks = Enumerable.Range(0, concurrentTasks)
                .Select(_ => _dicomService.ValidateDicomFileAsync(testFilePath));
            
            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().HaveCount(concurrentTasks);
            results.Should().OnlyContain(r => r == true);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }
}
