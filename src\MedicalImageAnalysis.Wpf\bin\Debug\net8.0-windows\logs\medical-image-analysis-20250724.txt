2025-07-24 09:57:22.234 +08:00 [INF] 医学影像解析系统启动中...
2025-07-24 09:57:22.263 +08:00 [INF] 应用程序版本: 1.0.0.0
2025-07-24 09:57:22.266 +08:00 [INF] 运行环境: Microsoft Windows NT 10.0.19045.0
2025-07-24 09:57:22.266 +08:00 [INF] 工作目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf
2025-07-24 09:57:22.271 +08:00 [WRN] MaterialDesign 主题初始化失败，使用默认主题
System.InvalidOperationException: Could not locate required resource with key(s) 'MaterialDesign.Brush.Primary.Light'
   at MaterialDesignThemes.Wpf.ResourceDictionaryExtensions.GetColor(ResourceDictionary resourceDictionary, String[] keys)
   at MaterialDesignThemes.Wpf.ResourceDictionaryExtensions.GetTheme(ResourceDictionary resourceDictionary)
   at MaterialDesignThemes.Wpf.PaletteHelper.GetTheme()
   at MedicalImageAnalysis.Wpf.App.InitializeMaterialDesign() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\App.xaml.cs:line 135
2025-07-24 09:57:22.319 +08:00 [INF] 依赖注入配置完成
2025-07-24 09:57:22.521 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-24 09:57:22.689 +08:00 [INF] 主窗口创建完成
2025-07-24 09:57:22.689 +08:00 [INF] 医学影像解析系统启动成功
2025-07-24 10:05:15.591 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“228”，行位置为“36”。
 ---> System.Exception: 无法找到名为“MaterialDesignLinearProgressBar”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 28
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 127
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 62
2025-07-24 10:05:17.259 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“228”，行位置为“36”。
 ---> System.Exception: 无法找到名为“MaterialDesignLinearProgressBar”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 28
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 127
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 62
2025-07-24 10:05:20.185 +08:00 [ERR] 导航到页面时发生错误: AnnotationMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“70”，行位置为“41”。
 ---> System.Exception: 无法找到名为“MaterialDesignFlatToggleButton”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.AnnotationView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\AnnotationView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.AnnotationView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\AnnotationView.xaml.cs:line 33
   at MedicalImageAnalysis.Wpf.MainWindow.ShowAnnotationPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 143
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 70
2025-07-24 10:05:20.707 +08:00 [ERR] 导航到页面时发生错误: ModelTrainingMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“303”，行位置为“48”。
 ---> System.Exception: 无法找到名为“MaterialDesignLinearProgressBar”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.ModelTrainingView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\ModelTrainingView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.ModelTrainingView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\ModelTrainingView.xaml.cs:line 26
   at MedicalImageAnalysis.Wpf.MainWindow.ShowModelTrainingPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 151
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 74
2025-07-24 10:05:30.217 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“228”，行位置为“36”。
 ---> System.Exception: 无法找到名为“MaterialDesignLinearProgressBar”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 28
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 127
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 62
2025-07-24 10:05:35.067 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“228”，行位置为“36”。
 ---> System.Exception: 无法找到名为“MaterialDesignLinearProgressBar”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 28
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 127
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 62
