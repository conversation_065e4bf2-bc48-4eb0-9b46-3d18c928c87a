using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// ModelTrainingView.xaml 的交互逻辑
    /// </summary>
    public partial class ModelTrainingView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<ModelTrainingView> _logger;
        private readonly ObservableCollection<TrainedModel> _trainedModels;
        private bool _isTraining = false;
        private CancellationTokenSource? _trainingCancellationTokenSource;

        public ModelTrainingView()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<ModelTrainingView>.Instance;
            _trainedModels = new ObservableCollection<TrainedModel>();
            TrainedModelsListView.ItemsSource = _trainedModels;

            // 初始化示例数据
            InitializeSampleData();
        }

        /// <summary>
        /// 初始化示例数据
        /// </summary>
        private void InitializeSampleData()
        {
            // 添加一些示例模型
            _trainedModels.Add(new TrainedModel
            {
                Name = "YOLOv11_Medical_v1.0",
                Accuracy = 89.5,
                CreatedTime = DateTime.Now.AddDays(-5),
                ModelPath = "./models/yolov11_medical_v1.pt"
            });

            _trainedModels.Add(new TrainedModel
            {
                Name = "YOLOv11_Fracture_v2.1",
                Accuracy = 92.3,
                CreatedTime = DateTime.Now.AddDays(-2),
                ModelPath = "./models/yolov11_fracture_v2.pt"
            });
        }

        /// <summary>
        /// 数据集选择变化
        /// </summary>
        private void DatasetComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DatasetComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var datasetName = selectedItem.Content.ToString();
                UpdateDatasetInfo(datasetName);
            }
        }

        /// <summary>
        /// 更新数据集信息
        /// </summary>
        private void UpdateDatasetInfo(string? datasetName)
        {
            // 模拟数据集信息
            switch (datasetName)
            {
                case "医学影像数据集 v1.0":
                    DatasetImageCountText.Text = "图像数量: 2,500";
                    DatasetAnnotationCountText.Text = "标注数量: 8,750";
                    DatasetSizeText.Text = "数据集大小: 1.2 GB";
                    DatasetClassesText.Text = "类别数量: 5";
                    break;
                case "DICOM标注数据集":
                    DatasetImageCountText.Text = "图像数量: 1,800";
                    DatasetAnnotationCountText.Text = "标注数量: 6,200";
                    DatasetSizeText.Text = "数据集大小: 950 MB";
                    DatasetClassesText.Text = "类别数量: 3";
                    break;
                case "自定义数据集":
                    DatasetImageCountText.Text = "图像数量: 待加载";
                    DatasetAnnotationCountText.Text = "标注数量: 待加载";
                    DatasetSizeText.Text = "数据集大小: 待加载";
                    DatasetClassesText.Text = "类别数量: 待加载";
                    break;
                default:
                    DatasetImageCountText.Text = "图像数量: 未选择";
                    DatasetAnnotationCountText.Text = "标注数量: 未选择";
                    DatasetSizeText.Text = "数据集大小: 未选择";
                    DatasetClassesText.Text = "类别数量: 未选择";
                    break;
            }
        }

        /// <summary>
        /// 浏览数据集
        /// </summary>
        private void BrowseDataset_Click(object sender, RoutedEventArgs e)
        {
            // 使用简单的输入对话框代替文件夹浏览器
            var folderPath = Microsoft.VisualBasic.Interaction.InputBox(
                "请输入数据集目录路径:",
                "选择数据集目录",
                @"C:\");

            if (!string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath))
            {
                try
                {
                    ValidateDatasetStructure(folderPath);
                    WpfMessageBox.Show($"数据集路径已设置：{folderPath}", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "浏览数据集时发生错误");
                    WpfMessageBox.Show($"设置数据集路径失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 验证数据集结构
        /// </summary>
        private void ValidateDatasetStructure(string datasetPath)
        {
            // 简化的数据集结构验证
            var requiredFolders = new[] { "images", "labels" };
            var missingFolders = new List<string>();

            foreach (var folder in requiredFolders)
            {
                var folderPath = Path.Combine(datasetPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    missingFolders.Add(folder);
                }
            }

            if (missingFolders.Any())
            {
                throw new DirectoryNotFoundException($"缺少必要的文件夹: {string.Join(", ", missingFolders)}");
            }
        }

        /// <summary>
        /// 验证数据集
        /// </summary>
        private async void ValidateDataset_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateTrainingStatus("正在验证数据集...", Brushes.Orange);

                // 模拟验证过程
                await Task.Delay(3000);

                var result = MessageBox.Show("数据集验证完成！\n\n发现问题：\n• 3个图像文件缺少对应标注\n• 2个标注文件格式不正确\n\n是否自动修复这些问题？",
                                           "验证结果", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdateTrainingStatus("正在修复数据集问题...", Brushes.Orange);
                    await Task.Delay(2000);
                    UpdateTrainingStatus("数据集验证和修复完成", Brushes.Green);
                }
                else
                {
                    UpdateTrainingStatus("数据集验证完成（存在问题）", Brushes.Orange);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证数据集时发生错误");
                MessageBox.Show($"验证数据集失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("数据集验证失败", Brushes.Red);
            }
        }

        /// <summary>
        /// 数据增强
        /// </summary>
        private async void DataAugmentation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("数据增强将生成额外的训练样本，这可能需要较长时间。是否继续？",
                                           "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdateTrainingStatus("正在进行数据增强...", Brushes.Orange);

                    // 模拟数据增强过程
                    await Task.Delay(5000);

                    UpdateTrainingStatus("数据增强完成", Brushes.Green);
                    MessageBox.Show("数据增强完成！\n\n生成了1,200个新的训练样本。", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);

                    // 更新数据集信息
                    DatasetImageCountText.Text = "图像数量: 3,700";
                    DatasetAnnotationCountText.Text = "标注数量: 12,950";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据增强时发生错误");
                MessageBox.Show($"数据增强失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("数据增强失败", Brushes.Red);
            }
        }

        /// <summary>
        /// 开始训练
        /// </summary>
        private async void StartTraining_Click(object sender, RoutedEventArgs e)
        {
            if (_isTraining)
            {
                MessageBox.Show("训练正在进行中，请等待完成或先停止当前训练。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 验证训练参数
            if (DatasetComboBox.SelectedItem == null)
            {
                MessageBox.Show("请先选择训练数据集。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                _isTraining = true;
                _trainingCancellationTokenSource = new CancellationTokenSource();

                // 更新UI状态
                StartTrainingButton.IsEnabled = false;
                PauseTrainingButton.IsEnabled = true;
                StopTrainingButton.IsEnabled = true;
                TrainingProgressCard.Visibility = Visibility.Visible;

                UpdateTrainingStatus("正在初始化训练...", Brushes.Orange);

                // 获取训练参数
                var epochs = (int)EpochsSlider.Value;
                var batchSize = (int)BatchSizeSlider.Value;
                var learningRate = LearningRateSlider.Value;
                var modelArchitecture = ModelArchitectureComboBox.Text;

                _logger.LogInformation("开始训练 - 模型: {Model}, 轮数: {Epochs}, 批次: {BatchSize}, 学习率: {LearningRate}",
                                     modelArchitecture, epochs, batchSize, learningRate);

                // 模拟训练过程
                await SimulateTraining(epochs, _trainingCancellationTokenSource.Token);

                if (!_trainingCancellationTokenSource.Token.IsCancellationRequested)
                {
                    // 训练完成，添加新模型
                    var newModel = new TrainedModel
                    {
                        Name = $"YOLOv11_Custom_{DateTime.Now:yyyyMMdd_HHmmss}",
                        Accuracy = 85.0 + new Random().NextDouble() * 10, // 模拟准确率
                        CreatedTime = DateTime.Now,
                        ModelPath = $"./models/yolov11_custom_{DateTime.Now:yyyyMMdd_HHmmss}.pt"
                    };

                    _trainedModels.Add(newModel);
                    UpdateTrainingStatus("训练完成！", Brushes.Green);

                    MessageBox.Show($"模型训练完成！\n\n模型名称: {newModel.Name}\n准确率: {newModel.Accuracy:F2}%",
                                  "训练完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (OperationCanceledException)
            {
                UpdateTrainingStatus("训练已取消", Brushes.Gray);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "训练过程中发生错误");
                MessageBox.Show($"训练失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("训练失败", Brushes.Red);
            }
            finally
            {
                // 重置UI状态
                _isTraining = false;
                StartTrainingButton.IsEnabled = true;
                PauseTrainingButton.IsEnabled = false;
                StopTrainingButton.IsEnabled = false;
                _trainingCancellationTokenSource?.Dispose();
                _trainingCancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 模拟训练过程
        /// </summary>
        private async Task SimulateTraining(int epochs, CancellationToken cancellationToken)
        {
            for (int epoch = 1; epoch <= epochs; epoch++)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 更新当前轮次
                CurrentEpochText.Text = $"{epoch}/{epochs}";

                // 模拟每个轮次的训练
                for (int step = 0; step < 100; step++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 更新进度
                    var epochProgress = (step + 1) * 100 / 100;
                    var overallProgress = ((epoch - 1) * 100 + epochProgress) * 100 / (epochs * 100);

                    EpochProgressBar.Value = epochProgress;
                    EpochProgressText.Text = $"{epochProgress}%";
                    OverallProgressBar.Value = overallProgress;
                    OverallProgressText.Text = $"{overallProgress:F1}%";

                    // 模拟损失和准确率变化
                    var trainingLoss = 2.0 - (epoch * 1.8 / epochs) + (new Random().NextDouble() - 0.5) * 0.1;
                    var validationLoss = 2.2 - (epoch * 1.9 / epochs) + (new Random().NextDouble() - 0.5) * 0.15;
                    var accuracy = (epoch * 85.0 / epochs) + (new Random().NextDouble() - 0.5) * 5;

                    TrainingLossText.Text = $"{Math.Max(0, trainingLoss):F3}";
                    ValidationLossText.Text = $"{Math.Max(0, validationLoss):F3}";
                    AccuracyText.Text = $"{Math.Max(0, Math.Min(100, accuracy)):F2}%";

                    UpdateTrainingStatus($"训练中 - 轮次 {epoch}/{epochs}", Brushes.Blue);

                    await Task.Delay(50, cancellationToken); // 模拟训练时间
                }

                // 轮次完成后稍作停顿
                await Task.Delay(200, cancellationToken);
            }
        }

        /// <summary>
        /// 暂停训练
        /// </summary>
        private void PauseTraining_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("暂停训练功能正在开发中。", "提示",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 停止训练
        /// </summary>
        private void StopTraining_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要停止训练吗？当前进度将会丢失。", "确认",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _trainingCancellationTokenSource?.Cancel();
                UpdateTrainingStatus("正在停止训练...", Brushes.Orange);
            }
        }

        /// <summary>
        /// 查看日志
        /// </summary>
        private void ViewLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logWindow = new Window
                {
                    Title = "训练日志",
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    Content = new ScrollViewer
                    {
                        Content = new TextBlock
                        {
                            Text = GenerateSampleLog(),
                            Margin = new Thickness(16),
                            FontFamily = new FontFamily("Consolas"),
                            FontSize = 12
                        }
                    }
                };
                logWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查看日志时发生错误");
                MessageBox.Show($"无法打开日志窗口：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 生成示例日志
        /// </summary>
        private string GenerateSampleLog()
        {
            return $@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 开始训练会话
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 模型架构: YOLOv11s
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 数据集: 医学影像数据集 v1.0
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 训练参数:
  - 轮数: 100
  - 批次大小: 8
  - 学习率: 0.001
  - 图像尺寸: 640x640
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 加载预训练权重
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 数据集验证完成
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 开始训练...
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 1/100 - Loss: 2.145, Val_Loss: 2.234, Accuracy: 12.5%
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 2/100 - Loss: 1.987, Val_Loss: 2.156, Accuracy: 18.3%
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 3/100 - Loss: 1.823, Val_Loss: 2.089, Accuracy: 24.7%
...
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 训练完成
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 最佳模型已保存";
        }

        /// <summary>
        /// 训练模型列表选择变化
        /// </summary>
        private void TrainedModelsListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 当选择模型时可以显示更多详细信息
        }

        /// <summary>
        /// 测试模型
        /// </summary>
        private void TestModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                MessageBox.Show($"模型测试功能正在开发中。\n\n选中模型: {model.Name}\n准确率: {model.Accuracy:F2}%",
                              "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("请先选择要测试的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 导出模型
        /// </summary>
        private void ExportModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出模型",
                    Filter = "PyTorch 模型 (*.pt)|*.pt|ONNX 模型 (*.onnx)|*.onnx|所有文件 (*.*)|*.*",
                    FileName = model.Name,
                    DefaultExt = "pt"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    try
                    {
                        // 模拟导出过程
                        MessageBox.Show($"模型已导出到: {saveFileDialog.FileName}", "导出成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "导出模型失败");
                        MessageBox.Show($"导出模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要导出的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 部署模型
        /// </summary>
        private void DeployModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var result = MessageBox.Show($"确定要部署模型 '{model.Name}' 吗？\n\n这将替换当前的生产模型。",
                                           "确认部署", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // 模拟部署过程
                        MessageBox.Show($"模型 '{model.Name}' 已成功部署到生产环境！", "部署成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "部署模型失败");
                        MessageBox.Show($"部署模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要部署的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 删除模型
        /// </summary>
        private void DeleteModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var result = MessageBox.Show($"确定要删除模型 '{model.Name}' 吗？\n\n此操作无法撤销。",
                                           "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _trainedModels.Remove(model);
                        MessageBox.Show($"模型 '{model.Name}' 已删除。", "删除成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "删除模型失败");
                        MessageBox.Show($"删除模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 更新训练状态
        /// </summary>
        private void UpdateTrainingStatus(string status, System.Windows.Media.Brush color)
        {
            TrainingStatusText.Text = status;
            TrainingStatusIcon.Foreground = color;
        }
    }

    /// <summary>
    /// 训练模型数据模型
    /// </summary>
    public class TrainedModel
    {
        public string Name { get; set; } = "";
        public double Accuracy { get; set; }
        public DateTime CreatedTime { get; set; }
        public string ModelPath { get; set; } = "";
    }
}
