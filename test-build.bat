@echo off
echo ========================================
echo 医学影像解析系统构建测试
echo ========================================

echo.
echo 检查 .NET SDK...
dotnet --version
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到 .NET SDK
    pause
    exit /b 1
)

echo.
echo 清理之前的构建...
dotnet clean

echo.
echo 恢复 NuGet 包...
dotnet restore
if %ERRORLEVEL% neq 0 (
    echo 错误: NuGet 包恢复失败
    pause
    exit /b 1
)

echo.
echo 构建 Core 项目...
dotnet build src\MedicalImageAnalysis.Core --configuration Debug --verbosity normal
if %ERRORLEVEL% neq 0 (
    echo 错误: Core 项目构建失败
    pause
    exit /b 1
)

echo.
echo 构建 Infrastructure 项目...
dotnet build src\MedicalImageAnalysis.Infrastructure --configuration Debug --verbosity normal
if %ERRORLEVEL% neq 0 (
    echo 错误: Infrastructure 项目构建失败
    pause
    exit /b 1
)

echo.
echo 构建 Application 项目...
dotnet build src\MedicalImageAnalysis.Application --configuration Debug --verbosity normal
if %ERRORLEVEL% neq 0 (
    echo 错误: Application 项目构建失败
    pause
    exit /b 1
)

echo.
echo 构建 API 项目...
dotnet build src\MedicalImageAnalysis.Api --configuration Debug --verbosity normal
if %ERRORLEVEL% neq 0 (
    echo 错误: API 项目构建失败
    pause
    exit /b 1
)

echo.
echo 构建 Web 项目...
dotnet build src\MedicalImageAnalysis.Web --configuration Debug --verbosity normal
if %ERRORLEVEL% neq 0 (
    echo 错误: Web 项目构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 所有项目构建成功！
echo ========================================
echo.
echo 现在可以运行项目了。
echo 使用 run-project.bat 或 run-project.ps1 启动项目。
echo.
pause
