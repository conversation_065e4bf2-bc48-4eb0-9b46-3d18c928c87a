{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/medical-image-analysis-web-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5002"}}, "Limits": {"MaxRequestBodySize": 524288000}}, "MedicalImageAnalysis": {"ApiBaseUrl": "http://localhost:5000", "MaxFileSize": 524288000, "SupportedFormats": ["dcm", "dicom"], "TempDirectory": "temp", "OutputDirectory": "output"}}