using System.Windows;
using MedicalImageAnalysis.WpfClient.ViewModels;

namespace MedicalImageAnalysis.WpfClient.Views;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        
        // 订阅ViewModel事件
        viewModel.RequestClose += (s, e) => Close();
        viewModel.RequestMinimize += (s, e) => WindowState = WindowState.Minimized;
        viewModel.RequestMaximize += (s, e) => 
        {
            WindowState = WindowState == WindowState.Maximized 
                ? WindowState.Normal 
                : WindowState.Maximized;
        };
        
        // 窗口加载完成后初始化
        Loaded += async (s, e) => await viewModel.InitializeAsync();
        
        // 窗口关闭时清理资源
        Closing += async (s, e) =>
        {
            e.Cancel = true;
            await viewModel.CleanupAsync();
            e.Cancel = false;
            Close();
        };
    }
}
