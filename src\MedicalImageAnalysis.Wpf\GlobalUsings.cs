// 全局命名空间，避免在每个文件中重复定义
global using System;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading;
global using System.Threading.Tasks;

// 全局命名空间别名，解决Windows Forms和WPF之间的命名冲突
global using WpfMessageBox = System.Windows.MessageBox;
global using WpfOpenFileDialog = Microsoft.Win32.OpenFileDialog;
global using WpfSaveFileDialog = Microsoft.Win32.SaveFileDialog;
global using WpfClipboard = System.Windows.Clipboard;
global using WpfDataFormats = System.Windows.DataFormats;
global using WpfDragEventArgs = System.Windows.DragEventArgs;
global using WpfDragDropEffects = System.Windows.DragDropEffects;
global using WpfMouseEventArgs = System.Windows.Input.MouseEventArgs;
global using WpfPoint = System.Windows.Point;
global using WpfSize = System.Windows.Size;
global using WpfRectangle = System.Windows.Shapes.Rectangle;
global using WpfPath = System.Windows.Shapes.Path;
global using WpfBrush = System.Windows.Media.Brush;
global using WpfBrushes = System.Windows.Media.Brushes;
global using WpfColor = System.Windows.Media.Color;
global using WpfColorConverter = System.Windows.Media.ColorConverter;
global using WpfFontFamily = System.Windows.Media.FontFamily;
global using WpfUserControl = System.Windows.Controls.UserControl;

// 保留System.IO.Path用于文件路径操作
global using IoPath = System.IO.Path;
