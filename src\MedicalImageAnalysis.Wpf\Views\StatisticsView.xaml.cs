using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// StatisticsView.xaml 的交互逻辑
    /// </summary>
    public partial class StatisticsView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<StatisticsView> _logger;
        private readonly ObservableCollection<ActivityItem> _recentActivities;
        private readonly DispatcherTimer _refreshTimer;
        private readonly Random _random;

        public StatisticsView()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<StatisticsView>.Instance;
            _recentActivities = new ObservableCollection<ActivityItem>();
            _random = new Random();

            RecentActivitiesListView.ItemsSource = _recentActivities;

            // 初始化数据
            InitializeData();

            // 绘制图表
            DrawTrendChart();
            DrawPieChart();

            // 设置定时刷新
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(30)
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
        }

        /// <summary>
        /// 初始化示例数据
        /// </summary>
        private void InitializeData()
        {
            // 添加示例活动
            _recentActivities.Add(new ActivityItem
            {
                Icon = "Upload",
                IconColor = "#2196F3",
                Action = "用户上传了5个DICOM文件",
                Time = DateTime.Now.AddMinutes(-5).ToString("HH:mm")
            });

            _recentActivities.Add(new ActivityItem
            {
                Icon = "Brain",
                IconColor = "#4CAF50",
                Action = "AI模型完成病灶检测",
                Time = DateTime.Now.AddMinutes(-12).ToString("HH:mm")
            });

            _recentActivities.Add(new ActivityItem
            {
                Icon = "Download",
                IconColor = "#FF9800",
                Action = "导出了标注数据集",
                Time = DateTime.Now.AddMinutes(-18).ToString("HH:mm")
            });

            _recentActivities.Add(new ActivityItem
            {
                Icon = "Settings",
                IconColor = "#9C27B0",
                Action = "系统配置已更新",
                Time = DateTime.Now.AddMinutes(-25).ToString("HH:mm")
            });

            _recentActivities.Add(new ActivityItem
            {
                Icon = "CheckCircle",
                IconColor = "#4CAF50",
                Action = "模型训练完成",
                Time = DateTime.Now.AddMinutes(-35).ToString("HH:mm")
            });
        }

        /// <summary>
        /// 绘制趋势图表
        /// </summary>
        private void DrawTrendChart()
        {
            TrendChart.Children.Clear();

            var width = 400.0;
            var height = 180.0;
            var margin = 20.0;

            // 模拟7天的数据
            var successData = new[] { 45, 52, 48, 65, 58, 72, 68 };
            var failureData = new[] { 5, 8, 6, 4, 7, 3, 5 };

            var maxValue = successData.Concat(failureData).Max() + 10;

            // 绘制网格线
            for (int i = 0; i <= 5; i++)
            {
                var y = margin + (height - 2 * margin) * i / 5;
                var gridLine = new Line
                {
                    X1 = margin,
                    Y1 = y,
                    X2 = width - margin,
                    Y2 = y,
                    Stroke = Brushes.LightGray,
                    StrokeThickness = 0.5
                };
                TrendChart.Children.Add(gridLine);
            }

            // 绘制成功处理线
            DrawLine(successData, maxValue, width, height, margin, Brushes.Blue, "成功处理");

            // 绘制失败处理线
            DrawLine(failureData, maxValue, width, height, margin, Brushes.Red, "处理失败");
        }

        /// <summary>
        /// 绘制线条
        /// </summary>
        private void DrawLine(int[] data, int maxValue, double width, double height, double margin, System.Windows.Media.Brush brush, string label)
        {
            var points = new PointCollection();

            for (int i = 0; i < data.Length; i++)
            {
                var x = margin + (width - 2 * margin) * i / (data.Length - 1);
                var y = height - margin - (height - 2 * margin) * data[i] / maxValue;
                points.Add(new Point(x, y));
            }

            var polyline = new Polyline
            {
                Points = points,
                Stroke = brush,
                StrokeThickness = 2,
                Fill = Brushes.Transparent
            };

            TrendChart.Children.Add(polyline);

            // 添加数据点
            foreach (var point in points)
            {
                var circle = new Ellipse
                {
                    Width = 6,
                    Height = 6,
                    Fill = brush,
                    Margin = new Thickness(point.X - 3, point.Y - 3, 0, 0)
                };
                TrendChart.Children.Add(circle);
            }
        }

        /// <summary>
        /// 绘制饼图
        /// </summary>
        private void DrawPieChart()
        {
            PieChart.Children.Clear();

            var centerX = 60.0;
            var centerY = 60.0;
            var radius = 50.0;

            var data = new[]
            {
                new { Value = 68.5, Color = "#2196F3" },
                new { Value = 18.2, Color = "#4CAF50" },
                new { Value = 10.1, Color = "#FF9800" },
                new { Value = 3.2, Color = "#9C27B0" }
            };

            double startAngle = 0;

            foreach (var item in data)
            {
                var angle = item.Value * 360 / 100;
                var endAngle = startAngle + angle;

                var path = CreatePieSlice(centerX, centerY, radius, startAngle, endAngle, item.Color);
                PieChart.Children.Add(path);

                startAngle = endAngle;
            }
        }

        /// <summary>
        /// 创建饼图扇形
        /// </summary>
        private System.Windows.Shapes.Path CreatePieSlice(double centerX, double centerY, double radius, double startAngle, double endAngle, string color)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = endAngle * Math.PI / 180;

            var x1 = centerX + radius * Math.Cos(startAngleRad);
            var y1 = centerY + radius * Math.Sin(startAngleRad);
            var x2 = centerX + radius * Math.Cos(endAngleRad);
            var y2 = centerY + radius * Math.Sin(endAngleRad);

            var largeArc = (endAngle - startAngle) > 180;

            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure { StartPoint = new Point(centerX, centerY) };

            pathFigure.Segments.Add(new LineSegment(new Point(x1, y1), true));
            pathFigure.Segments.Add(new ArcSegment(
                new Point(x2, y2),
                new Size(radius, radius),
                0,
                largeArc,
                SweepDirection.Clockwise,
                true));
            pathFigure.Segments.Add(new LineSegment(new Point(centerX, centerY), true));

            pathGeometry.Figures.Add(pathFigure);

            return new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Stroke = Brushes.White,
                StrokeThickness = 1
            };
        }

        /// <summary>
        /// 定时刷新事件
        /// </summary>
        private void RefreshTimer_Tick(object? sender, EventArgs e)
        {
            UpdatePerformanceMetrics();
            UpdateStatistics();
        }

        /// <summary>
        /// 更新性能指标
        /// </summary>
        private void UpdatePerformanceMetrics()
        {
            // 模拟性能数据变化
            var cpuUsage = 30 + _random.NextDouble() * 40;
            var memoryUsage = 50 + _random.NextDouble() * 30;
            var diskUsage = 20 + _random.NextDouble() * 20;

            CpuUsageBar.Value = cpuUsage;
            CpuUsageText.Text = $"{cpuUsage:F0}%";

            MemoryUsageBar.Value = memoryUsage;
            MemoryUsageText.Text = $"{memoryUsage:F0}%";

            DiskUsageBar.Value = diskUsage;
            DiskUsageText.Text = $"{diskUsage:F0}%";
        }

        /// <summary>
        /// 更新统计数据
        /// </summary>
        private void UpdateStatistics()
        {
            // 模拟统计数据变化
            var totalProcessed = 1200 + _random.Next(0, 100);
            var successRate = 90 + _random.NextDouble() * 8;
            var avgTime = 2.0 + _random.NextDouble() * 1.5;
            var activeUsers = 20 + _random.Next(0, 10);

            TotalProcessedText.Text = totalProcessed.ToString("N0");
            SuccessRateText.Text = $"{successRate:F1}%";
            AvgProcessingTimeText.Text = $"{avgTime:F1}s";
            ActiveUsersText.Text = activeUsers.ToString();
        }

        /// <summary>
        /// 趋势周期选择变化
        /// </summary>
        private void TrendPeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TrendChart != null)
            {
                DrawTrendChart(); // 重新绘制图表
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 刷新所有数据
                UpdateStatistics();
                UpdatePerformanceMetrics();
                DrawTrendChart();
                DrawPieChart();

                // 添加新的活动记录
                var newActivity = new ActivityItem
                {
                    Icon = "Refresh",
                    IconColor = "#2196F3",
                    Action = "数据已刷新",
                    Time = DateTime.Now.ToString("HH:mm")
                };

                _recentActivities.Insert(0, newActivity);

                // 保持最多10条记录
                while (_recentActivities.Count > 10)
                {
                    _recentActivities.RemoveAt(_recentActivities.Count - 1);
                }

                MessageBox.Show("数据刷新完成！", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新数据时发生错误");
                MessageBox.Show($"刷新数据失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出报告
        /// </summary>
        private void ExportReport_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出统计报告",
                Filter = "Excel 文件 (*.xlsx)|*.xlsx|PDF 文件 (*.pdf)|*.pdf|文本文件 (*.txt)|*.txt",
                FileName = $"统计报告_{DateTime.Now:yyyyMMdd_HHmmss}",
                DefaultExt = "txt"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportReportToFile(saveFileDialog.FileName);
                    MessageBox.Show($"报告已导出到：{saveFileDialog.FileName}", "导出成功",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "导出报告失败");
                    MessageBox.Show($"导出报告失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 导出报告到文件
        /// </summary>
        private void ExportReportToFile(string filePath)
        {
            var report = GenerateReport();
            File.WriteAllText(filePath, report);
        }

        /// <summary>
        /// 生成报告内容
        /// </summary>
        private string GenerateReport()
        {
            var report = $@"医学影像解析系统 - 统计报告
生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

=== 系统概览 ===
总处理数量: {TotalProcessedText.Text}
成功率: {SuccessRateText.Text}
平均处理时间: {AvgProcessingTimeText.Text}
活跃用户数: {ActiveUsersText.Text}

=== 性能指标 ===
CPU使用率: {CpuUsageText.Text}
内存使用率: {MemoryUsageText.Text}
磁盘使用率: {DiskUsageText.Text}

=== 文件类型分布 ===
DICOM (.dcm): 68.5%
PNG: 18.2%
JPEG: 10.1%
其他: 3.2%

=== 系统状态 ===
API服务: 正常
数据库: 正常
AI模型: 负载高
存储: 正常

=== 最近活动 ===";

            foreach (var activity in _recentActivities.Take(10))
            {
                report += $"\n[{activity.Time}] {activity.Action}";
            }

            report += $@"

=== 报告说明 ===
本报告包含了系统的主要运行指标和统计数据。
数据更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
报告有效期: 24小时

© 2024 医学影像解析系统";

            return report;
        }
    }

    /// <summary>
    /// 活动项目数据模型
    /// </summary>
    public class ActivityItem
    {
        public string Icon { get; set; } = "";
        public string IconColor { get; set; } = "";
        public string Action { get; set; } = "";
        public string Time { get; set; } = "";
    }
}
