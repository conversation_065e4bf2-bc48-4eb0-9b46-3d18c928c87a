@using Microsoft.AspNetCore.Components.Routing

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="fas fa-heartbeat me-2"></i>
            医学影像解析
        </a>
        <button title="导航菜单" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="CollapseNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <i class="fas fa-home me-2" aria-hidden="true"></i> 首页
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="dicom-upload">
                <i class="fas fa-cloud-upload-alt me-2" aria-hidden="true"></i> DICOM 上传
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="image-processing">
                <i class="fas fa-images me-2" aria-hidden="true"></i> 影像处理
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="annotation">
                <i class="fas fa-tags me-2" aria-hidden="true"></i> 智能标注
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="model-training">
                <i class="fas fa-cogs me-2" aria-hidden="true"></i> 模型训练
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="dataset-export">
                <i class="fas fa-download me-2" aria-hidden="true"></i> 数据集导出
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="statistics">
                <i class="fas fa-chart-bar me-2" aria-hidden="true"></i> 统计分析
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="directory-manager">
                <i class="fas fa-folder me-2" aria-hidden="true"></i> 目录管理
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="settings">
                <i class="fas fa-cog me-2" aria-hidden="true"></i> 系统设置
            </NavLink>
        </div>

        <hr class="my-3" />

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="help">
                <i class="fas fa-question-circle me-2" aria-hidden="true"></i> 帮助文档
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="about">
                <i class="fas fa-info-circle me-2" aria-hidden="true"></i> 关于系统
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void CollapseNavMenu()
    {
        collapseNavMenu = true;
    }
}
