2025-07-24 09:10:54.146 +08:00 [INF] 启动医学影像解析系统 Web 应用
2025-07-24 09:10:54.219 +08:00 [INF] Now listening on: http://localhost:5002
2025-07-24 09:10:54.226 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 09:10:54.227 +08:00 [INF] Hosting environment: Production
2025-07-24 09:10:54.228 +08:00 [INF] Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web
2025-07-24 09:12:37.934 +08:00 [INF] Application is shutting down...
[2025-07-24 09:12:51.610 +08:00 INF] : 启动医学影像解析系统 Web 应用 {}
[2025-07-24 09:12:51.695 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5002 {"EventId":{"Id":14,"Name":"ListeningOnAddress"}}
[2025-07-24 09:12:51.702 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down. {}
[2025-07-24 09:12:51.704 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Production {}
[2025-07-24 09:12:51.705 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web {}
[2025-07-24 09:14:19.968 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"RequestId":"0HNEA9TLCJ0T6:********","RequestPath":"/test","ConnectionId":"0HNEA9TLCJ0T6"}
[2025-07-24 09:14:19.991 +08:00 ERR] Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware: An unhandled exception has occurred while executing the request. {"EventId":{"Id":1,"Name":"UnhandledException"},"RequestId":"0HNEA9TLCJ0T6:********","RequestPath":"/test","ConnectionId":"0HNEA9TLCJ0T6"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /Pages/_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
[2025-07-24 09:14:20.011 +08:00 ERR] Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware: An exception was thrown attempting to execute the error handler. {"EventId":{"Id":3,"Name":"Exception"},"RequestId":"0HNEA9TLCJ0T6:********","RequestPath":"/test","ConnectionId":"0HNEA9TLCJ0T6"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /Pages/_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.HandleException(HttpContext context, ExceptionDispatchInfo edi)
[2025-07-24 09:14:20.024 +08:00 ERR] Microsoft.AspNetCore.Server.Kestrel: Connection id "0HNEA9TLCJ0T6", Request id "0HNEA9TLCJ0T6:********": An unhandled exception was thrown by the application. {"EventId":{"Id":13,"Name":"ApplicationError"},"RequestId":"0HNEA9TLCJ0T6:********","RequestPath":"/test"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /Pages/_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.HandleException(HttpContext context, ExceptionDispatchInfo edi)
   at Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddlewareImpl.<Invoke>g__Awaited|10_0(ExceptionHandlerMiddlewareImpl middleware, HttpContext context, Task task)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
[2025-07-24 09:14:56.306 +08:00 INF] Microsoft.Hosting.Lifetime: Application is shutting down... {}
