using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Models;
using MedicalImageAnalysis.WpfClient.Services;
using MedicalImageAnalysis.WpfClient.Views;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows;

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly IApiService _apiService;
    private readonly ISignalRService _signalRService;
    private readonly IDialogService _dialogService;
    private readonly INotificationService _notificationService;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _loadingMessage = "";

    [ObservableProperty]
    private double _progressValue = 0;

    [ObservableProperty]
    private bool _isProgressVisible = false;

    [ObservableProperty]
    private ConnectionStatus _connectionStatus = ConnectionStatus.Disconnected;

    [ObservableProperty]
    private string _connectionStatusText = "未连接";

    [ObservableProperty]
    private string _systemInfo = "";

    [ObservableProperty]
    private NavigationItem? _selectedNavigationItem;

    [ObservableProperty]
    private object? _currentView;

    public ObservableCollection<NavigationItem> NavigationItems { get; } = new();

    // 事件（这些事件在MainWindow.xaml.cs中被订阅）
#pragma warning disable CS0067 // 事件从不使用
    public event EventHandler? RequestClose;
    public event EventHandler? RequestMinimize;
    public event EventHandler? RequestMaximize;
#pragma warning restore CS0067

    public MainWindowViewModel(
        ILogger<MainWindowViewModel> logger,
        IApiService apiService,
        ISignalRService signalRService,
        IDialogService dialogService,
        INotificationService notificationService)
    {
        _logger = logger;
        _apiService = apiService;
        _signalRService = signalRService;
        _dialogService = dialogService;
        _notificationService = notificationService;

        InitializeNavigationItems();
        InitializeCommands();
        UpdateSystemInfo();
    }

    /// <summary>
    /// 初始化导航项
    /// </summary>
    private void InitializeNavigationItems()
    {
        NavigationItems.Add(new NavigationItem
        {
            Title = "首页",
            Icon = "Home",
            ViewType = typeof(DashboardView)
        });

        NavigationItems.Add(new NavigationItem
        {
            Title = "DICOM查看器",
            Icon = "FileImage",
            ViewType = typeof(DicomViewerView)
        });

        NavigationItems.Add(new NavigationItem
        {
            Title = "模型训练",
            Icon = "School",
            ViewType = typeof(ModelTrainingView)
        });

        NavigationItems.Add(new NavigationItem
        {
            Title = "智能标注",
            Icon = "Draw",
            ViewType = typeof(AnnotationView)
        });

        NavigationItems.Add(new NavigationItem
        {
            Title = "数据管理",
            Icon = "Database",
            ViewType = typeof(DataManagementView)
        });

        NavigationItems.Add(new NavigationItem
        {
            Title = "系统监控",
            Icon = "Monitor",
            ViewType = typeof(SystemMonitorView)
        });

        // 默认选择首页
        SelectedNavigationItem = NavigationItems.First();
    }

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        // 监听导航项变化
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(SelectedNavigationItem))
            {
                NavigateToView();
            }
        };
    }

    /// <summary>
    /// 初始化异步操作
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            SetLoading(true, "正在初始化...");

            // 连接到API服务
            await ConnectToApiAsync();

            // 连接到SignalR
            await ConnectToSignalRAsync();

            // 加载初始视图
            NavigateToView();

            StatusMessage = "初始化完成";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化失败");
            StatusMessage = $"初始化失败: {ex.Message}";
            await _notificationService.ShowErrorAsync("初始化失败", ex.Message);
        }
        finally
        {
            SetLoading(false);
        }
    }

    /// <summary>
    /// 连接到API服务
    /// </summary>
    private async Task ConnectToApiAsync()
    {
        try
        {
            var isConnected = await _apiService.TestConnectionAsync();
            ConnectionStatus = isConnected ? ConnectionStatus.Connected : ConnectionStatus.Disconnected;
            ConnectionStatusText = isConnected ? "已连接" : "连接失败";

            if (isConnected)
            {
                _logger.LogInformation("成功连接到API服务");
            }
            else
            {
                _logger.LogWarning("无法连接到API服务");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接API服务失败");
            ConnectionStatus = ConnectionStatus.Error;
            ConnectionStatusText = "连接错误";
        }
    }

    /// <summary>
    /// 连接到SignalR
    /// </summary>
    private async Task ConnectToSignalRAsync()
    {
        try
        {
            await _signalRService.StartAsync();
            
            // 订阅SignalR事件
            _signalRService.OnProgressUpdate += OnProgressUpdate;
            _signalRService.OnStatusUpdate += OnStatusUpdate;
            _signalRService.OnNotification += OnNotification;

            _logger.LogInformation("成功连接到SignalR服务");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接SignalR服务失败");
        }
    }

    /// <summary>
    /// 导航到视图
    /// </summary>
    private void NavigateToView()
    {
        if (SelectedNavigationItem?.ViewType != null)
        {
            try
            {
                // 这里应该通过依赖注入创建视图
                // 简化实现，直接创建实例
                var view = Activator.CreateInstance(SelectedNavigationItem.ViewType);
                CurrentView = view;
                
                StatusMessage = $"已切换到 {SelectedNavigationItem.Title}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到视图失败: {ViewType}", SelectedNavigationItem.ViewType.Name);
                StatusMessage = "导航失败";
            }
        }
    }

    /// <summary>
    /// 设置加载状态
    /// </summary>
    private void SetLoading(bool isLoading, string message = "")
    {
        IsLoading = isLoading;
        LoadingMessage = message;
    }

    /// <summary>
    /// 更新系统信息
    /// </summary>
    private void UpdateSystemInfo()
    {
        var now = DateTime.Now;
        SystemInfo = $"版本 1.0.0 | {now:yyyy-MM-dd HH:mm:ss}";
    }

    /// <summary>
    /// 进度更新事件处理
    /// </summary>
    private void OnProgressUpdate(double progress)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            ProgressValue = progress;
            IsProgressVisible = progress > 0 && progress < 100;
        });
    }

    /// <summary>
    /// 状态更新事件处理
    /// </summary>
    private void OnStatusUpdate(string status)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = status;
        });
    }

    /// <summary>
    /// 通知事件处理
    /// </summary>
    private void OnNotification(string type, string title, string message)
    {
        Application.Current.Dispatcher.Invoke(async () =>
        {
            switch (type.ToLower())
            {
                case "success":
                    await _notificationService.ShowSuccessAsync(title, message);
                    break;
                case "warning":
                    await _notificationService.ShowWarningAsync(title, message);
                    break;
                case "error":
                    await _notificationService.ShowErrorAsync(title, message);
                    break;
                default:
                    await _notificationService.ShowInfoAsync(title, message);
                    break;
            }
        });
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    public async Task CleanupAsync()
    {
        try
        {
            if (_signalRService != null)
            {
                _signalRService.OnProgressUpdate -= OnProgressUpdate;
                _signalRService.OnStatusUpdate -= OnStatusUpdate;
                _signalRService.OnNotification -= OnNotification;
                
                await _signalRService.StopAsync();
            }

            _logger.LogInformation("资源清理完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理资源时发生错误");
        }
    }

    // 命令
    [RelayCommand]
    private async Task OpenDicom()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "选择DICOM文件",
                "DICOM文件 (*.dcm)|*.dcm|所有文件 (*.*)|*.*");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 切换到DICOM查看器
                SelectedNavigationItem = NavigationItems.FirstOrDefault(x => x.Title == "DICOM查看器");
                
                // 通知DICOM查看器加载文件
                // 这里需要通过消息传递或事件来实现
                StatusMessage = $"已打开文件: {Path.GetFileName(filePath)}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开DICOM文件失败");
            await _notificationService.ShowErrorAsync("错误", "打开DICOM文件失败");
        }
    }

    [RelayCommand]
    private void StartTraining()
    {
        // 切换到模型训练页面
        SelectedNavigationItem = NavigationItems.FirstOrDefault(x => x.Title == "模型训练");
    }

    [RelayCommand]
    private void RunInference()
    {
        // 切换到智能标注页面
        SelectedNavigationItem = NavigationItems.FirstOrDefault(x => x.Title == "智能标注");
    }

    [RelayCommand]
    private async Task Settings()
    {
        try
        {
            await _dialogService.ShowSettingsDialogAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开设置对话框失败");
        }
    }

    [RelayCommand]
    private async Task About()
    {
        try
        {
            await _dialogService.ShowAboutDialogAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开关于对话框失败");
        }
    }
}
