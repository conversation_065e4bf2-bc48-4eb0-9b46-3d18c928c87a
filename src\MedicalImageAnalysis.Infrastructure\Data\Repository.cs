using Microsoft.EntityFrameworkCore;
using MedicalImageAnalysis.Core.Interfaces;
using System.Linq.Expressions;

namespace MedicalImageAnalysis.Infrastructure.Data;

/// <summary>
/// 通用仓储实现
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class Repository<T> : IRepository<T> where T : class
{
    protected readonly MedicalImageDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public Repository(MedicalImageDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.ToListAsync(cancellationToken);
    }

    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task<T?> FindFirstAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public virtual async Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        if (orderBy != null)
        {
            query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        }

        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        var entry = await _dbSet.AddAsync(entity, cancellationToken);
        return entry.Entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddRangeAsync(entities, cancellationToken);
        return entities;
    }

    public virtual Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        _dbSet.Update(entity);
        return Task.FromResult(entity);
    }

    public virtual Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    public virtual async Task DeleteByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            await DeleteAsync(entity, cancellationToken);
        }
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        _dbSet.RemoveRange(entities);
        return Task.CompletedTask;
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(predicate, cancellationToken);
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
        {
            return await _dbSet.CountAsync(cancellationToken);
        }
        return await _dbSet.CountAsync(predicate, cancellationToken);
    }

    public virtual async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }
}

/// <summary>
/// 工作单元实现
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly MedicalImageDbContext _context;
    private Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction? _transaction;

    // 仓储实例
    private IRepository<MedicalImageAnalysis.Core.Entities.Patient>? _patients;
    private IRepository<MedicalImageAnalysis.Core.Entities.DicomStudy>? _studies;
    private IRepository<MedicalImageAnalysis.Core.Entities.DicomSeries>? _series;
    private IRepository<MedicalImageAnalysis.Core.Entities.DicomInstance>? _instances;
    private IRepository<MedicalImageAnalysis.Core.Entities.Annotation>? _annotations;
    private IRepository<MedicalImageAnalysis.Core.Entities.AnnotationProject>? _annotationProjects;
    private IRepository<MedicalImageAnalysis.Core.Entities.AnnotationTemplate>? _annotationTemplates;
    private IRepository<MedicalImageAnalysis.Core.Entities.TrainingJob>? _trainingJobs;
    private IRepository<MedicalImageAnalysis.Core.Entities.TrainingDataset>? _trainingDatasets;
    private IRepository<MedicalImageAnalysis.Core.Entities.ModelVersion>? _modelVersions;
    private IRepository<MedicalImageAnalysis.Core.Entities.TrainingMetric>? _trainingMetrics;
    private IRepository<MedicalImageAnalysis.Core.Entities.ProcessingTask>? _processingTasks;
    private IRepository<MedicalImageAnalysis.Core.Entities.ProcessingResult>? _processingResults;
    private IRepository<MedicalImageAnalysis.Core.Entities.ProcessingLog>? _processingLogs;
    private IRepository<MedicalImageAnalysis.Core.Entities.SystemConfiguration>? _systemConfigurations;
    private IRepository<MedicalImageAnalysis.Core.Entities.UserPreference>? _userPreferences;
    private IRepository<MedicalImageAnalysis.Core.Entities.AuditLog>? _auditLogs;

    public UnitOfWork(MedicalImageDbContext context)
    {
        _context = context;
    }

    public IRepository<MedicalImageAnalysis.Core.Entities.Patient> Patients =>
        _patients ??= new Repository<MedicalImageAnalysis.Core.Entities.Patient>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.DicomStudy> Studies =>
        _studies ??= new Repository<MedicalImageAnalysis.Core.Entities.DicomStudy>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.DicomSeries> Series =>
        _series ??= new Repository<MedicalImageAnalysis.Core.Entities.DicomSeries>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.DicomInstance> Instances =>
        _instances ??= new Repository<MedicalImageAnalysis.Core.Entities.DicomInstance>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.Annotation> Annotations =>
        _annotations ??= new Repository<MedicalImageAnalysis.Core.Entities.Annotation>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.AnnotationProject> AnnotationProjects =>
        _annotationProjects ??= new Repository<MedicalImageAnalysis.Core.Entities.AnnotationProject>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.AnnotationTemplate> AnnotationTemplates =>
        _annotationTemplates ??= new Repository<MedicalImageAnalysis.Core.Entities.AnnotationTemplate>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.TrainingJob> TrainingJobs =>
        _trainingJobs ??= new Repository<MedicalImageAnalysis.Core.Entities.TrainingJob>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.TrainingDataset> TrainingDatasets =>
        _trainingDatasets ??= new Repository<MedicalImageAnalysis.Core.Entities.TrainingDataset>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.ModelVersion> ModelVersions =>
        _modelVersions ??= new Repository<MedicalImageAnalysis.Core.Entities.ModelVersion>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.TrainingMetric> TrainingMetrics =>
        _trainingMetrics ??= new Repository<MedicalImageAnalysis.Core.Entities.TrainingMetric>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.ProcessingTask> ProcessingTasks =>
        _processingTasks ??= new Repository<MedicalImageAnalysis.Core.Entities.ProcessingTask>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.ProcessingResult> ProcessingResults =>
        _processingResults ??= new Repository<MedicalImageAnalysis.Core.Entities.ProcessingResult>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.ProcessingLog> ProcessingLogs =>
        _processingLogs ??= new Repository<MedicalImageAnalysis.Core.Entities.ProcessingLog>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.SystemConfiguration> SystemConfigurations =>
        _systemConfigurations ??= new Repository<MedicalImageAnalysis.Core.Entities.SystemConfiguration>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.UserPreference> UserPreferences =>
        _userPreferences ??= new Repository<MedicalImageAnalysis.Core.Entities.UserPreference>(_context);

    public IRepository<MedicalImageAnalysis.Core.Entities.AuditLog> AuditLogs =>
        _auditLogs ??= new Repository<MedicalImageAnalysis.Core.Entities.AuditLog>(_context);

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
