using System.Windows.Controls;
using System.Windows.Input;

namespace MedicalImageAnalysis.WpfClient.Views;

/// <summary>
/// AnnotationView.xaml 的交互逻辑
/// </summary>
public partial class AnnotationView : UserControl
{
    private bool _isDrawing = false;

    public AnnotationView()
    {
        InitializeComponent();
    }

    private void AnnotationCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (sender is Canvas canvas)
        {
            _isDrawing = true;
            canvas.CaptureMouse();
            
            var position = e.GetPosition(canvas);
            // 这里可以添加标注开始的逻辑
            // 例如：开始绘制矩形、多边形等
        }
    }

    private void AnnotationCanvas_MouseMove(object sender, MouseEventArgs e)
    {
        if (_isDrawing && sender is Canvas canvas)
        {
            var position = e.GetPosition(canvas);
            // 这里可以添加标注绘制过程的逻辑
            // 例如：更新矩形大小、添加多边形点等
        }
    }

    private void AnnotationCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (sender is Canvas canvas)
        {
            _isDrawing = false;
            canvas.ReleaseMouseCapture();
            
            var position = e.GetPosition(canvas);
            // 这里可以添加标注完成的逻辑
            // 例如：完成矩形绘制、结束多边形等
        }
    }
}
