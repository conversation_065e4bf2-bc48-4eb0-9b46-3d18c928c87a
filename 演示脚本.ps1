# 医学影像解析系统 - 完整演示脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "医学影像解析系统 - 完整演示" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 API 服务状态
Write-Host "检查 API 服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method Get -TimeoutSec 5
    Write-Host "✓ API 服务正常运行: $($response.status)" -ForegroundColor Green
    Write-Host "  时间戳: $($response.timestamp)" -ForegroundColor Gray
    Write-Host "  版本: $($response.version)" -ForegroundColor Gray
} catch {
    Write-Host "✗ API 服务未运行，正在启动..." -ForegroundColor Red
    
    # 启动 API 服务
    Write-Host "启动 API 服务..." -ForegroundColor Yellow
    $env:ASPNETCORE_ENVIRONMENT = "Development"
    $apiProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project src\MedicalImageAnalysis.Api" -WindowStyle Minimized -PassThru
    
    Write-Host "等待 API 服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 8
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method Get -TimeoutSec 10
        Write-Host "✓ API 服务启动成功: $($response.status)" -ForegroundColor Green
    } catch {
        Write-Host "✗ API 服务启动失败" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "功能演示菜单" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "1. 测试 DICOM 文件验证" -ForegroundColor White
Write-Host "2. 测试 DICOM 元数据提取" -ForegroundColor White
Write-Host "3. 查看系统目录信息" -ForegroundColor White
Write-Host "4. 启动 WPF 桌面应用" -ForegroundColor White
Write-Host "5. 打开 Swagger 文档" -ForegroundColor White
Write-Host "6. 运行完整演示" -ForegroundColor White
Write-Host "0. 退出" -ForegroundColor White
Write-Host ""

do {
    $choice = Read-Host "请选择功能 (0-6)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "测试 DICOM 文件验证..." -ForegroundColor Green
            
            if (Test-Path "Brain\DJ01.dcm") {
                try {
                    $form = @{
                        file = Get-Item "Brain\DJ01.dcm"
                    }
                    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/study/validate" -Method Post -Form $form
                    
                    Write-Host "✓ 验证结果:" -ForegroundColor Green
                    Write-Host "  文件有效: $($response.isValid)" -ForegroundColor Cyan
                    Write-Host "  文件大小: $($response.fileSize) 字节" -ForegroundColor Cyan
                    Write-Host "  包含像素数据: $($response.hasPixelData)" -ForegroundColor Cyan
                    Write-Host "  SOP 类 UID: $($response.sopClassUid)" -ForegroundColor Cyan
                } catch {
                    Write-Host "✗ 验证失败: $($_.Exception.Message)" -ForegroundColor Red
                }
            } else {
                Write-Host "✗ 示例文件 Brain\DJ01.dcm 不存在" -ForegroundColor Red
            }
        }
        
        "2" {
            Write-Host ""
            Write-Host "测试 DICOM 元数据提取..." -ForegroundColor Green
            
            if (Test-Path "Brain\DJ01.dcm") {
                try {
                    $form = @{
                        file = Get-Item "Brain\DJ01.dcm"
                    }
                    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/study/metadata" -Method Post -Form $form
                    
                    Write-Host "✓ 元数据提取成功:" -ForegroundColor Green
                    Write-Host "  患者姓名: $($response.PatientName)" -ForegroundColor Cyan
                    Write-Host "  患者 ID: $($response.PatientID)" -ForegroundColor Cyan
                    Write-Host "  检查日期: $($response.StudyDate)" -ForegroundColor Cyan
                    Write-Host "  模态: $($response.Modality)" -ForegroundColor Cyan
                    Write-Host "  序列描述: $($response.SeriesDescription)" -ForegroundColor Cyan
                } catch {
                    Write-Host "✗ 元数据提取失败: $($_.Exception.Message)" -ForegroundColor Red
                }
            } else {
                Write-Host "✗ 示例文件 Brain\DJ01.dcm 不存在" -ForegroundColor Red
            }
        }
        
        "3" {
            Write-Host ""
            Write-Host "查看系统目录信息..." -ForegroundColor Green
            
            try {
                $response = Invoke-RestMethod -Uri "http://localhost:5000/api/directory/system-directories" -Method Get
                
                Write-Host "✓ 系统目录信息:" -ForegroundColor Green
                $response | ForEach-Object {
                    Write-Host "  $($_.Name): $($_.Path)" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "✗ 获取系统目录信息失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        "4" {
            Write-Host ""
            Write-Host "启动 WPF 桌面应用..." -ForegroundColor Green
            
            try {
                Start-Process -FilePath "dotnet" -ArgumentList "run --project src\MedicalImageAnalysis.Wpf" -WindowStyle Normal
                Write-Host "✓ WPF 应用已启动" -ForegroundColor Green
            } catch {
                Write-Host "✗ WPF 应用启动失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        "5" {
            Write-Host ""
            Write-Host "打开 Swagger 文档..." -ForegroundColor Green
            
            try {
                Start-Process "http://localhost:5000/swagger"
                Write-Host "✓ Swagger 文档已在浏览器中打开" -ForegroundColor Green
            } catch {
                Write-Host "✗ 无法打开浏览器: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "请手动访问: http://localhost:5000/swagger" -ForegroundColor Yellow
            }
        }
        
        "6" {
            Write-Host ""
            Write-Host "运行完整演示..." -ForegroundColor Green
            
            # 依次执行所有演示
            & $MyInvocation.MyCommand.Path "1"
            Start-Sleep -Seconds 2
            & $MyInvocation.MyCommand.Path "2"
            Start-Sleep -Seconds 2
            & $MyInvocation.MyCommand.Path "3"
            Start-Sleep -Seconds 2
            & $MyInvocation.MyCommand.Path "4"
            Start-Sleep -Seconds 2
            & $MyInvocation.MyCommand.Path "5"
            
            Write-Host ""
            Write-Host "✓ 完整演示完成！" -ForegroundColor Green
        }
        
        "0" {
            Write-Host ""
            Write-Host "退出演示..." -ForegroundColor Yellow
            break
        }
        
        default {
            Write-Host "无效选择，请重新输入。" -ForegroundColor Red
        }
    }
    
    if ($choice -ne "0") {
        Write-Host ""
        Write-Host "按任意键继续..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Write-Host ""
    }
    
} while ($choice -ne "0")

Write-Host "感谢使用医学影像解析系统！" -ForegroundColor Green
