#!/bin/bash

# 医学影像解析系统测试运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
医学影像解析系统测试运行脚本

用法: $0 [选项] <命令>

命令:
  unit            运行单元测试
  integration     运行集成测试
  all             运行所有测试
  coverage        运行测试并生成覆盖率报告
  watch           监视模式运行测试
  clean           清理测试输出

选项:
  -h, --help      显示此帮助信息
  -v, --verbose   详细输出
  -f, --filter    测试过滤器
  -o, --output    输出目录
  --no-build      跳过构建
  --parallel      并行运行测试

示例:
  $0 unit -v
  $0 integration --no-build
  $0 coverage -o ./test-results
  $0 all -f "DicomService"
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v dotnet &> /dev/null; then
        log_error "未找到 .NET CLI"
        exit 1
    fi
    
    # 检查.NET版本
    local dotnet_version=$(dotnet --version)
    log_info ".NET 版本: $dotnet_version"
    
    # 检查测试项目
    if [[ ! -f "tests/MedicalImageAnalysis.Tests.Unit/MedicalImageAnalysis.Tests.Unit.csproj" ]]; then
        log_error "未找到单元测试项目"
        exit 1
    fi
    
    if [[ ! -f "tests/MedicalImageAnalysis.Tests.Integration/MedicalImageAnalysis.Tests.Integration.csproj" ]]; then
        log_error "未找到集成测试项目"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 清理测试输出
clean_test_output() {
    log_info "清理测试输出..."
    
    # 清理测试结果目录
    if [[ -d "TestResults" ]]; then
        rm -rf TestResults
    fi
    
    # 清理覆盖率报告
    if [[ -d "coverage" ]]; then
        rm -rf coverage
    fi
    
    # 清理临时文件
    find . -name "*.trx" -delete
    find . -name "*.coverage" -delete
    find . -name "coverage.*.xml" -delete
    
    log_success "清理完成"
}

# 构建解决方案
build_solution() {
    if [[ "$NO_BUILD" == "true" ]]; then
        log_info "跳过构建"
        return
    fi
    
    log_info "构建解决方案..."
    
    dotnet build --configuration Release --no-restore
    
    if [[ $? -ne 0 ]]; then
        log_error "构建失败"
        exit 1
    fi
    
    log_success "构建完成"
}

# 还原NuGet包
restore_packages() {
    log_info "还原NuGet包..."
    
    dotnet restore
    
    if [[ $? -ne 0 ]]; then
        log_error "包还原失败"
        exit 1
    fi
    
    log_success "包还原完成"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    local test_args=""
    
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args --verbosity detailed"
    fi
    
    if [[ -n "$FILTER" ]]; then
        test_args="$test_args --filter \"$FILTER\""
    fi
    
    if [[ "$NO_BUILD" == "true" ]]; then
        test_args="$test_args --no-build"
    fi
    
    if [[ "$PARALLEL" == "true" ]]; then
        test_args="$test_args --parallel"
    fi
    
    if [[ -n "$OUTPUT_DIR" ]]; then
        mkdir -p "$OUTPUT_DIR"
        test_args="$test_args --logger trx --results-directory \"$OUTPUT_DIR\""
    fi
    
    eval "dotnet test tests/MedicalImageAnalysis.Tests.Unit/MedicalImageAnalysis.Tests.Unit.csproj --configuration Release $test_args"
    
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "单元测试通过"
    else
        log_error "单元测试失败"
        return $exit_code
    fi
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 启动测试依赖服务
    start_test_dependencies
    
    local test_args=""
    
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args --verbosity detailed"
    fi
    
    if [[ -n "$FILTER" ]]; then
        test_args="$test_args --filter \"$FILTER\""
    fi
    
    if [[ "$NO_BUILD" == "true" ]]; then
        test_args="$test_args --no-build"
    fi
    
    if [[ -n "$OUTPUT_DIR" ]]; then
        mkdir -p "$OUTPUT_DIR"
        test_args="$test_args --logger trx --results-directory \"$OUTPUT_DIR\""
    fi
    
    eval "dotnet test tests/MedicalImageAnalysis.Tests.Integration/MedicalImageAnalysis.Tests.Integration.csproj --configuration Release $test_args"
    
    local exit_code=$?
    
    # 停止测试依赖服务
    stop_test_dependencies
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "集成测试通过"
    else
        log_error "集成测试失败"
        return $exit_code
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "运行所有测试..."
    
    run_unit_tests
    local unit_exit_code=$?
    
    run_integration_tests
    local integration_exit_code=$?
    
    if [[ $unit_exit_code -eq 0 && $integration_exit_code -eq 0 ]]; then
        log_success "所有测试通过"
        return 0
    else
        log_error "部分测试失败"
        return 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    log_info "生成覆盖率报告..."
    
    # 检查是否安装了coverlet和reportgenerator
    if ! dotnet tool list -g | grep -q "coverlet.console"; then
        log_info "安装coverlet工具..."
        dotnet tool install -g coverlet.console
    fi
    
    if ! dotnet tool list -g | grep -q "dotnet-reportgenerator-globaltool"; then
        log_info "安装reportgenerator工具..."
        dotnet tool install -g dotnet-reportgenerator-globaltool
    fi
    
    local coverage_dir="${OUTPUT_DIR:-coverage}"
    mkdir -p "$coverage_dir"
    
    # 运行单元测试并收集覆盖率
    log_info "收集单元测试覆盖率..."
    dotnet test tests/MedicalImageAnalysis.Tests.Unit/MedicalImageAnalysis.Tests.Unit.csproj \
        --configuration Release \
        --collect:"XPlat Code Coverage" \
        --results-directory "$coverage_dir/unit"
    
    # 运行集成测试并收集覆盖率
    log_info "收集集成测试覆盖率..."
    start_test_dependencies
    
    dotnet test tests/MedicalImageAnalysis.Tests.Integration/MedicalImageAnalysis.Tests.Integration.csproj \
        --configuration Release \
        --collect:"XPlat Code Coverage" \
        --results-directory "$coverage_dir/integration"
    
    stop_test_dependencies
    
    # 合并覆盖率报告
    log_info "生成覆盖率报告..."
    reportgenerator \
        -reports:"$coverage_dir/**/coverage.cobertura.xml" \
        -targetdir:"$coverage_dir/report" \
        -reporttypes:"Html;Cobertura;JsonSummary"
    
    log_success "覆盖率报告已生成: $coverage_dir/report/index.html"
    
    # 显示覆盖率摘要
    if [[ -f "$coverage_dir/report/Summary.json" ]]; then
        local line_coverage=$(cat "$coverage_dir/report/Summary.json" | grep -o '"linecoverage":[0-9.]*' | cut -d':' -f2)
        local branch_coverage=$(cat "$coverage_dir/report/Summary.json" | grep -o '"branchcoverage":[0-9.]*' | cut -d':' -f2)
        
        log_info "行覆盖率: ${line_coverage}%"
        log_info "分支覆盖率: ${branch_coverage}%"
    fi
}

# 监视模式运行测试
run_tests_watch() {
    log_info "启动测试监视模式..."
    
    local test_project="tests/MedicalImageAnalysis.Tests.Unit/MedicalImageAnalysis.Tests.Unit.csproj"
    
    if [[ "$TEST_TYPE" == "integration" ]]; then
        test_project="tests/MedicalImageAnalysis.Tests.Integration/MedicalImageAnalysis.Tests.Integration.csproj"
    fi
    
    dotnet watch test "$test_project" --configuration Release
}

# 启动测试依赖服务
start_test_dependencies() {
    log_info "启动测试依赖服务..."
    
    # 检查Docker是否可用
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        # 启动测试数据库
        docker-compose -f docker-compose.test.yml up -d sqlserver redis
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 10
    else
        log_warning "Docker不可用，跳过依赖服务启动"
    fi
}

# 停止测试依赖服务
stop_test_dependencies() {
    log_info "停止测试依赖服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.test.yml down
    fi
}

# 解析命令行参数
VERBOSE=false
NO_BUILD=false
PARALLEL=false
FILTER=""
OUTPUT_DIR=""
TEST_TYPE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -f|--filter)
            FILTER="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        unit|integration|all|coverage|watch|clean)
            COMMAND="$1"
            if [[ "$1" == "unit" || "$1" == "integration" ]]; then
                TEST_TYPE="$1"
            fi
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查命令
if [[ -z "$COMMAND" ]]; then
    log_error "请指定命令"
    show_help
    exit 1
fi

# 主执行逻辑
main() {
    log_info "医学影像解析系统测试运行脚本"
    log_info "命令: $COMMAND"
    
    check_dependencies
    
    case $COMMAND in
        clean)
            clean_test_output
            ;;
        unit)
            restore_packages
            build_solution
            run_unit_tests
            ;;
        integration)
            restore_packages
            build_solution
            run_integration_tests
            ;;
        all)
            restore_packages
            build_solution
            run_all_tests
            ;;
        coverage)
            restore_packages
            build_solution
            generate_coverage_report
            ;;
        watch)
            restore_packages
            run_tests_watch
            ;;
        *)
            log_error "未知命令: $COMMAND"
            exit 1
            ;;
    esac
}

# 执行主函数
main
