using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// DICOM 序列实体，表示一个影像序列
/// </summary>
public class DicomSeries
{
    /// <summary>
    /// 序列唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// DICOM 序列实例 UID
    /// </summary>
    [Required]
    [StringLength(64)]
    public string SeriesInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 序列号
    /// </summary>
    public int SeriesNumber { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    [StringLength(256)]
    public string SeriesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 模态类型 (CT, MR, US, etc.)
    /// </summary>
    [StringLength(16)]
    public string Modality { get; set; } = string.Empty;

    /// <summary>
    /// 序列日期时间
    /// </summary>
    public DateTime SeriesDateTime { get; set; }

    /// <summary>
    /// 序列日期
    /// </summary>
    public DateTime SeriesDate { get; set; }

    /// <summary>
    /// 序列时间
    /// </summary>
    public TimeSpan SeriesTime { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    [StringLength(64)]
    public string BodyPartExamined { get; set; } = string.Empty;

    /// <summary>
    /// 所属研究ID
    /// </summary>
    public Guid StudyId { get; set; }

    /// <summary>
    /// 所属研究
    /// </summary>
    public DicomStudy Study { get; set; } = null!;

    /// <summary>
    /// 包含的实例集合
    /// </summary>
    public List<DicomInstance> Instances { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 获取序列的图像尺寸
    /// </summary>
    public (int Width, int Height) ImageSize
    {
        get
        {
            var firstInstance = Instances.FirstOrDefault();
            return firstInstance != null ? (firstInstance.Columns, firstInstance.Rows) : (0, 0);
        }
    }

    /// <summary>
    /// 获取序列的切片数量
    /// </summary>
    public int SliceCount => Instances.Count;

    /// <summary>
    /// 检查序列是否完整
    /// </summary>
    public bool IsComplete
    {
        get
        {
            if (!Instances.Any()) return false;
            
            // 检查实例号是否连续
            var instanceNumbers = Instances.Select(i => i.InstanceNumber).OrderBy(n => n).ToList();
            for (int i = 1; i < instanceNumbers.Count; i++)
            {
                if (instanceNumbers[i] - instanceNumbers[i - 1] != 1)
                    return false;
            }
            return true;
        }
    }

    /// <summary>
    /// 获取序列的像素数据类型
    /// </summary>
    public PixelDataType PixelDataType
    {
        get
        {
            var firstInstance = Instances.FirstOrDefault();
            return firstInstance?.PixelDataType ?? PixelDataType.Unknown;
        }
    }
}

/// <summary>
/// 像素数据类型枚举
/// </summary>
public enum PixelDataType
{
    /// <summary>
    /// 未知类型
    /// </summary>
    Unknown = 0,
    /// <summary>
    /// 无符号8位整数
    /// </summary>
    UnsignedInt8 = 1,
    /// <summary>
    /// 有符号8位整数
    /// </summary>
    SignedInt8 = 2,
    /// <summary>
    /// 无符号16位整数
    /// </summary>
    UnsignedInt16 = 3,
    /// <summary>
    /// 有符号16位整数
    /// </summary>
    SignedInt16 = 4,
    /// <summary>
    /// 无符号32位整数
    /// </summary>
    UnsignedInt32 = 5,
    /// <summary>
    /// 有符号32位整数
    /// </summary>
    SignedInt32 = 6,
    /// <summary>
    /// 32位浮点数
    /// </summary>
    Float32 = 7,
    /// <summary>
    /// 64位浮点数
    /// </summary>
    Float64 = 8
}
