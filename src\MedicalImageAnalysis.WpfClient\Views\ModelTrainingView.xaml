<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.ModelTrainingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="School" Width="24" Height="24" VerticalAlignment="Center"/>
                <TextBlock Text="YOLOv11模型训练" 
                         FontSize="18" 
                         FontWeight="Medium"
                         VerticalAlignment="Center"
                         Margin="12,0,0,0"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧配置区域 -->
                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                    <!-- 训练配置 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="训练配置" Style="{StaticResource SubtitleText}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 左列 -->
                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBox materialDesign:HintAssist.Hint="实验名称"
                                           Text="{Binding ExperimentName}"
                                           Style="{StaticResource CustomTextBox}"/>

                                    <ComboBox materialDesign:HintAssist.Hint="模型类型"
                                            ItemsSource="{Binding ModelTypes}"
                                            SelectedItem="{Binding SelectedModelType}"
                                            Style="{StaticResource CustomComboBox}"/>

                                    <TextBox materialDesign:HintAssist.Hint="训练轮数"
                                           Text="{Binding Epochs}"
                                           Style="{StaticResource CustomTextBox}"/>

                                    <TextBox materialDesign:HintAssist.Hint="批次大小"
                                           Text="{Binding BatchSize}"
                                           Style="{StaticResource CustomTextBox}"/>
                                </StackPanel>

                                <!-- 右列 -->
                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <TextBox materialDesign:HintAssist.Hint="学习率"
                                           Text="{Binding LearningRate}"
                                           Style="{StaticResource CustomTextBox}"/>

                                    <TextBox materialDesign:HintAssist.Hint="图像尺寸"
                                           Text="{Binding ImageSize}"
                                           Style="{StaticResource CustomTextBox}"/>

                                    <CheckBox Content="使用预训练权重"
                                            IsChecked="{Binding UsePretrainedWeights}"
                                            Style="{StaticResource CustomCheckBox}"/>

                                    <CheckBox Content="启用数据增强"
                                            IsChecked="{Binding EnableDataAugmentation}"
                                            Style="{StaticResource CustomCheckBox}"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 数据集配置 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="数据集配置" Style="{StaticResource SubtitleText}"/>

                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" 
                                       materialDesign:HintAssist.Hint="数据集路径"
                                       Text="{Binding DatasetPath}"
                                       Style="{StaticResource CustomTextBox}"/>
                                <Button Grid.Column="1" 
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Command="{Binding BrowseDatasetCommand}"
                                      Margin="8,8,0,8">
                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                </Button>
                            </Grid>

                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" 
                                       materialDesign:HintAssist.Hint="输出目录"
                                       Text="{Binding OutputDirectory}"
                                       Style="{StaticResource CustomTextBox}"/>
                                <Button Grid.Column="1" 
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Command="{Binding BrowseOutputCommand}"
                                      Margin="8,8,0,8">
                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                </Button>
                            </Grid>

                            <!-- 数据集统计 -->
                            <Expander Header="数据集统计" Margin="0,16,0,0">
                                <Grid Margin="16,8,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="训练集" FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding TrainImageCount}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="{StaticResource PrimaryBrush}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="验证集" FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ValidImageCount}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="{StaticResource SecondaryBrush}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="类别数" FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ClassCount}" 
                                                 FontSize="24" 
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="{StaticResource AccentBrush}"/>
                                    </StackPanel>
                                </Grid>
                            </Expander>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 训练控制 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="训练控制" Style="{StaticResource SubtitleText}"/>

                            <UniformGrid Columns="3" Margin="0,16">
                                <Button Style="{StaticResource PrimaryButton}"
                                      Command="{Binding StartTrainingCommand}"
                                      IsEnabled="{Binding CanStartTraining}"
                                      Margin="0,0,4,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Play" Width="16" Height="16"/>
                                        <TextBlock Text="开始训练" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource SecondaryButton}"
                                      Command="{Binding PauseTrainingCommand}"
                                      IsEnabled="{Binding CanPauseTraining}"
                                      Margin="2,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Pause" Width="16" Height="16"/>
                                        <TextBlock Text="暂停训练" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Command="{Binding StopTrainingCommand}"
                                      IsEnabled="{Binding CanStopTraining}"
                                      Margin="4,0,0,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Stop" Width="16" Height="16"/>
                                        <TextBlock Text="停止训练" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Button>
                            </UniformGrid>

                            <!-- 训练进度 -->
                            <StackPanel Visibility="{Binding IsTraining, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Grid Margin="0,16,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="训练进度" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="1" Text="{Binding TrainingProgress, StringFormat={}{0:F1}%}"/>
                                </Grid>
                                <ProgressBar Value="{Binding TrainingProgress}" 
                                           Maximum="100"
                                           Style="{StaticResource CustomProgressBar}"/>

                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding CurrentEpochText}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding EstimatedTimeText}" HorizontalAlignment="Right"/>
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- 右侧监控区域 -->
                <StackPanel Grid.Column="1">
                    <!-- 训练状态 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="训练状态" Style="{StaticResource SubtitleText}"/>

                            <Grid Margin="0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="状态:" FontWeight="Medium" Margin="0,4"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TrainingStatus}" Margin="8,4,0,4"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="损失:" FontWeight="Medium" Margin="0,4"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentLoss, StringFormat=F4}" Margin="8,4,0,4"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="精度:" FontWeight="Medium" Margin="0,4"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentAccuracy, StringFormat=P2}" Margin="8,4,0,4"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="mAP:" FontWeight="Medium" Margin="0,4"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentMAP, StringFormat=F3}" Margin="8,4,0,4"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 训练日志 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="训练日志" Style="{StaticResource SubtitleText}"/>
                                <Button Grid.Column="1" 
                                      Style="{StaticResource MaterialDesignToolButton}"
                                      Command="{Binding ClearLogCommand}"
                                      ToolTip="清空日志">
                                    <materialDesign:PackIcon Kind="Delete"/>
                                </Button>
                            </Grid>

                            <ScrollViewer Height="200" 
                                        VerticalScrollBarVisibility="Auto"
                                        Margin="0,8">
                                <TextBox Text="{Binding TrainingLog}" 
                                       IsReadOnly="True"
                                       TextWrapping="Wrap"
                                       FontFamily="{StaticResource MonospaceFont}"
                                       FontSize="11"
                                       Background="Transparent"
                                       BorderThickness="0"/>
                            </ScrollViewer>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 快速操作 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="快速操作" Style="{StaticResource SubtitleText}"/>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding ValidateModelCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,8,0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CheckCircle" Width="16" Height="16"/>
                                    <TextBlock Text="验证模型" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding ExportModelCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Export" Width="16" Height="16"/>
                                    <TextBlock Text="导出模型" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding ViewResultsCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16"/>
                                    <TextBlock Text="查看结果" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
