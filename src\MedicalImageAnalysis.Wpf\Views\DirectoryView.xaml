<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.DirectoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <materialDesign:PackIcon Kind="Folder"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Margin="0,0,12,0"/>
            <TextBlock Text="文件与目录管理"
                     FontSize="28"
                     FontWeight="Medium"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：目录树 -->
            <materialDesign:Card Grid.Column="0"
                               Margin="0,0,8,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 目录树标题 -->
                    <Border Grid.Row="0"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,0,0,1"
                          Padding="16,12">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="目录结构"
                                     FontWeight="Medium"
                                     VerticalAlignment="Center"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="刷新目录"
                                  Margin="8,0,0,0"
                                  Click="RefreshDirectory_Click">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- 目录树 -->
                    <TreeView x:Name="DirectoryTreeView"
                            Grid.Row="1"
                            Margin="8"
                            SelectedItemChanged="DirectoryTreeView_SelectedItemChanged">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="{Binding Icon}"
                                                           Width="16" Height="16"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding Name}"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </Grid>
            </materialDesign:Card>

            <!-- 中间：文件列表 -->
            <materialDesign:Card Grid.Column="1"
                               Margin="8,0,8,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 工具栏 -->
                    <Border Grid.Row="0"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,0,0,1"
                          Padding="16,12">
                        <StackPanel Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"
                                  Click="CreateFolder_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderPlus"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="新建文件夹"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"
                                  Click="UploadFiles_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Upload"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="上传文件"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"
                                  Click="DeleteSelected_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Delete"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="删除"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- 路径导航 -->
                    <Border Grid.Row="1"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderOpen"
                                                   Width="16" Height="16"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="CurrentPathText"
                                     Text="./data"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- 文件列表 -->
                    <ListView x:Name="FileListView"
                            Grid.Row="2"
                            Margin="8"
                            SelectionChanged="FileListView_SelectionChanged"
                            MouseDoubleClick="FileListView_MouseDoubleClick">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="名称" Width="200">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="{Binding Icon}"
                                                                       Width="16" Height="16"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Name}"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="类型" Width="80" DisplayMemberBinding="{Binding Type}"/>
                                <GridViewColumn Header="大小" Width="80" DisplayMemberBinding="{Binding Size}"/>
                                <GridViewColumn Header="修改时间" Width="120" DisplayMemberBinding="{Binding ModifiedTime}"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <!-- 状态栏 -->
                    <Border Grid.Row="3"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="FileCountText"
                                     Text="0 个项目"
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text=" | "
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="8,0"/>
                            <TextBlock x:Name="TotalSizeText"
                                     Text="总大小: 0 B"
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧：文件详情 -->
            <materialDesign:Card Grid.Column="2"
                               Margin="8,0,0,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="文件详情"
                                 FontSize="18"
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 文件预览 -->
                        <materialDesign:Card x:Name="FilePreviewCard"
                                           Background="{DynamicResource MaterialDesignCardBackground}"
                                           Margin="0,0,0,16"
                                           Visibility="Collapsed">
                            <StackPanel Margin="16">
                                <TextBlock Text="预览" FontWeight="Medium" Margin="0,0,0,8"/>
                                <Border Height="150"
                                      Background="Black"
                                      BorderBrush="{DynamicResource MaterialDesignDivider}"
                                      BorderThickness="1">
                                    <Image x:Name="FilePreviewImage"
                                         Stretch="Uniform"
                                         RenderOptions.BitmapScalingMode="HighQuality"/>
                                </Border>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 文件信息 -->
                        <materialDesign:Card x:Name="FileInfoCard"
                                           Background="{DynamicResource MaterialDesignCardBackground}"
                                           Margin="0,0,0,16"
                                           Visibility="Collapsed">
                            <StackPanel Margin="16">
                                <TextBlock Text="文件信息" FontWeight="Medium" Margin="0,0,0,12"/>

                                <StackPanel x:Name="FileInfoPanel">
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="文件名:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileNameText" Text="" TextWrapping="Wrap"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="类型:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileTypeText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="大小:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileSizeText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="创建时间:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileCreatedText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="修改时间:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileModifiedText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="路径:" Width="60" FontWeight="Medium"/>
                                        <TextBlock x:Name="FilePathText" Text="" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- DICOM 信息 -->
                        <materialDesign:Card x:Name="DicomInfoCard"
                                           Background="{DynamicResource MaterialDesignCardBackground}"
                                           Margin="0,0,0,16"
                                           Visibility="Collapsed">
                            <StackPanel Margin="16">
                                <TextBlock Text="DICOM 信息" FontWeight="Medium" Margin="0,0,0,12"/>

                                <StackPanel x:Name="DicomInfoPanel">
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="患者姓名:" Width="80" FontWeight="Medium"/>
                                        <TextBlock x:Name="PatientNameText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="患者ID:" Width="80" FontWeight="Medium"/>
                                        <TextBlock x:Name="PatientIdText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="检查日期:" Width="80" FontWeight="Medium"/>
                                        <TextBlock x:Name="StudyDateText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="模态:" Width="80" FontWeight="Medium"/>
                                        <TextBlock x:Name="ModalityText" Text=""/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <TextBlock Text="序列描述:" Width="80" FontWeight="Medium"/>
                                        <TextBlock x:Name="SeriesDescText" Text="" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 文件操作 -->
                        <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}">
                            <StackPanel Margin="16">
                                <TextBlock Text="文件操作" FontWeight="Medium" Margin="0,0,0,12"/>

                                <Button Content="打开文件"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="OpenFile_Click"/>
                                <Button Content="复制路径"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="CopyPath_Click"/>
                                <Button Content="重命名"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="RenameFile_Click"/>
                                <Button Content="移动到..."
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="MoveFile_Click"/>
                                <Button Content="删除文件"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="DeleteFile_Click"/>

                                <Separator Margin="0,12"/>

                                <Button Content="属性"
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Margin="0,4"
                                      Click="ShowProperties_Click"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
