{"format": 1, "restore": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj": {}}, "projects": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj", "projectName": "MedicalImageAnalysis.Core", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "fo-dicom": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj", "projectName": "MedicalImageAnalysis.Infrastructure", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "false", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.16.3, )"}, "Microsoft.ML.OnnxRuntime.Gpu": {"target": "Package", "version": "[1.16.3, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.6, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "fo-dicom": {"target": "Package", "version": "[5.1.2, )"}, "fo-dicom.Imaging.ImageSharp": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}