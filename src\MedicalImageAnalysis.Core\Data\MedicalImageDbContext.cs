using Microsoft.EntityFrameworkCore;
using MedicalImageAnalysis.Core.Entities;
using System.Text.Json;

namespace MedicalImageAnalysis.Core.Data;

/// <summary>
/// 医学影像数据库上下文
/// </summary>
public class MedicalImageDbContext : DbContext
{
    public MedicalImageDbContext(DbContextOptions<MedicalImageDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 患者表
    /// </summary>
    public DbSet<Patient> Patients { get; set; } = null!;

    /// <summary>
    /// DICOM研究表
    /// </summary>
    public DbSet<DicomStudy> Studies { get; set; } = null!;

    /// <summary>
    /// DICOM序列表
    /// </summary>
    public DbSet<DicomSeries> Series { get; set; } = null!;

    /// <summary>
    /// DICOM实例表
    /// </summary>
    public DbSet<DicomInstance> Instances { get; set; } = null!;

    /// <summary>
    /// 标注表
    /// </summary>
    public DbSet<Annotation> Annotations { get; set; } = null!;

    /// <summary>
    /// 处理结果表
    /// </summary>
    public DbSet<ProcessingResult> ProcessingResults { get; set; } = null!;

    /// <summary>
    /// 训练任务表
    /// </summary>
    public DbSet<TrainingJob> TrainingJobs { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置Patient实体
        modelBuilder.Entity<Patient>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.PatientId).IsUnique();
            entity.Property(e => e.PatientId).IsRequired().HasMaxLength(64);
            entity.Property(e => e.PatientName).HasMaxLength(256);
            entity.Property(e => e.PatientSex).HasMaxLength(1);
            entity.Property(e => e.PatientAge).HasMaxLength(4);
            entity.Property(e => e.PatientWeight).HasMaxLength(16);
            entity.Property(e => e.PatientSize).HasMaxLength(16);
            entity.Property(e => e.EthnicGroup).HasMaxLength(16);
            entity.Property(e => e.PatientComments).HasMaxLength(1024);
        });

        // 配置DicomStudy实体
        modelBuilder.Entity<DicomStudy>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.StudyInstanceUid).IsUnique();
            entity.Property(e => e.StudyInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.StudyId).HasMaxLength(16);
            entity.Property(e => e.StudyDescription).HasMaxLength(64);
            entity.Property(e => e.AccessionNumber).HasMaxLength(16);
            entity.Property(e => e.ReferringPhysicianName).HasMaxLength(64);
            entity.Property(e => e.InstitutionName).HasMaxLength(64);
            entity.Property(e => e.InstitutionAddress).HasMaxLength(1024);
            entity.Property(e => e.StationName).HasMaxLength(16);
            entity.Property(e => e.StudyDescription).HasMaxLength(64);
            entity.Property(e => e.SeriesDescription).HasMaxLength(64);
            entity.Property(e => e.InstitutionalDepartmentName).HasMaxLength(64);
            entity.Property(e => e.PhysiciansOfRecord).HasMaxLength(64);
            entity.Property(e => e.PerformingPhysicianName).HasMaxLength(64);
            entity.Property(e => e.NameOfPhysiciansReadingStudy).HasMaxLength(64);
            entity.Property(e => e.OperatorsName).HasMaxLength(64);
            entity.Property(e => e.AdmittingDiagnosesDescription).HasMaxLength(64);
            entity.Property(e => e.ManufacturerModelName).HasMaxLength(64);
            entity.Property(e => e.ReferencedPatientSequence).HasMaxLength(64);
            entity.Property(e => e.DerivationDescription).HasMaxLength(1024);
            entity.Property(e => e.BodyPart).HasMaxLength(16);
            entity.Property(e => e.ScanOptions).HasMaxLength(64);
            entity.Property(e => e.ContrastBolusAgent).HasMaxLength(64);

            // 配置与Patient的关系
            entity.HasOne(e => e.Patient)
                  .WithMany(p => p.Studies)
                  .HasForeignKey(e => e.PatientId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置DicomSeries实体
        modelBuilder.Entity<DicomSeries>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.SeriesInstanceUid).IsUnique();
            entity.Property(e => e.SeriesInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.SeriesDescription).HasMaxLength(256);
            entity.Property(e => e.Modality).HasMaxLength(16);

            // 配置与DicomStudy的关系
            entity.HasOne(e => e.Study)
                  .WithMany(s => s.Series)
                  .HasForeignKey(e => e.StudyId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置DicomInstance实体
        modelBuilder.Entity<DicomInstance>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.SopInstanceUid).IsUnique();
            entity.Property(e => e.SopInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.SopClassUid).HasMaxLength(64);
            entity.Property(e => e.TransferSyntaxUid).HasMaxLength(64);
            entity.Property(e => e.PhotometricInterpretation).HasMaxLength(32);
            entity.Property(e => e.FilePath).HasMaxLength(512);
            entity.Property(e => e.FileHash).HasMaxLength(64);

            // 配置复杂类型的序列化
            entity.Property(e => e.PixelSpacing)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<(double X, double Y)>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ImagePosition)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<(double X, double Y, double Z)>(v, (JsonSerializerOptions?)null));

            entity.Property(e => e.ImageOrientationPatient)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<double[]>(v, (JsonSerializerOptions?)null) ?? new double[6]);

            // 配置与DicomSeries的关系
            entity.HasOne(e => e.Series)
                  .WithMany(s => s.Instances)
                  .HasForeignKey(e => e.SeriesId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置Annotation实体
        modelBuilder.Entity<Annotation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Label).IsRequired().HasMaxLength(64);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.VerifiedBy).HasMaxLength(128);

            // 配置复杂类型的序列化
            entity.OwnsOne(e => e.BoundingBox, bb =>
            {
                bb.Property(b => b.CenterX);
                bb.Property(b => b.CenterY);
                bb.Property(b => b.Width);
                bb.Property(b => b.Height);
            });

            entity.Property(e => e.PolygonPoints)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<List<Point2D>>(v, (JsonSerializerOptions?)null) ?? new List<Point2D>());

            // 配置与DicomInstance的关系
            entity.HasOne(e => e.Instance)
                  .WithMany(i => i.Annotations)
                  .HasForeignKey(e => e.InstanceId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置ProcessingResult实体
        modelBuilder.Entity<ProcessingResult>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProcessorName).HasMaxLength(128);
            entity.Property(e => e.ProcessorVersion).HasMaxLength(32);
            entity.Property(e => e.ErrorMessage).HasMaxLength(1024);
            entity.Property(e => e.OutputPath).HasMaxLength(512);

            // 配置复杂类型的序列化
            entity.Property(e => e.Parameters)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

            entity.Property(e => e.Metrics)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, double>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, double>());

            // 配置与DicomStudy的关系
            entity.HasOne(e => e.Study)
                  .WithMany(s => s.ProcessingResults)
                  .HasForeignKey(e => e.StudyId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置TrainingJob实体
        modelBuilder.Entity<TrainingJob>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.JobName).IsRequired().HasMaxLength(128);
            entity.Property(e => e.ModelType).HasMaxLength(64);
            entity.Property(e => e.ModelPath).HasMaxLength(512);
            entity.Property(e => e.DatasetPath).HasMaxLength(512);
            entity.Property(e => e.ConfigPath).HasMaxLength(512);
            entity.Property(e => e.OutputPath).HasMaxLength(512);
            entity.Property(e => e.LogPath).HasMaxLength(512);
            entity.Property(e => e.ErrorMessage).HasMaxLength(1024);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);

            // 配置复杂类型的序列化
            entity.Property(e => e.HyperParameters)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

            entity.Property(e => e.TrainingMetrics)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, double>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, double>());

            entity.Property(e => e.ValidationMetrics)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, double>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, double>());
        });
    }
}
