<Application x:Class="MedicalImageAnalysis.WpfClient.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局颜色 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#00BCD4"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            
            <!-- 全局字体 -->
            <FontFamily x:Key="PrimaryFont">Microsoft YaHei UI</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas</FontFamily>
            
            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            
            <!-- 数据模板 -->
            <DataTemplate x:Key="LoadingTemplate">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Loading"
                                           Width="24" Height="24"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="加载中..." 
                             VerticalAlignment="Center"
                             FontFamily="{StaticResource PrimaryFont}"/>
                </StackPanel>
            </DataTemplate>
            
            <DataTemplate x:Key="ErrorTemplate">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Width="24" Height="24" 
                                           Foreground="{StaticResource ErrorBrush}"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding}" 
                             VerticalAlignment="Center"
                             Foreground="{StaticResource ErrorBrush}"
                             FontFamily="{StaticResource PrimaryFont}"/>
                </StackPanel>
            </DataTemplate>
        </ResourceDictionary>
    </Application.Resources>
</Application>
