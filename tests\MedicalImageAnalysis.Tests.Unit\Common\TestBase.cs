using AutoFixture;
using AutoFixture.Xunit2;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Infrastructure.Data;
using MedicalImageAnalysis.Core.Interfaces;
using Moq;
using Xunit;

namespace MedicalImageAnalysis.Tests.Unit.Common;

/// <summary>
/// 测试基类，提供通用的测试设置和工具
/// </summary>
public abstract class TestBase : IDisposable
{
    protected readonly IFixture Fixture;
    protected readonly Mock<ILogger> MockLogger;
    protected readonly ServiceProvider ServiceProvider;
    protected readonly MedicalImageDbContext DbContext;

    protected TestBase()
    {
        // 配置AutoFixture
        Fixture = new Fixture();
        Fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => Fixture.Behaviors.Remove(b));
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // 配置Mock Logger
        MockLogger = new Mock<ILogger>();

        // 配置服务容器
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();

        // 配置内存数据库
        DbContext = ServiceProvider.GetRequiredService<MedicalImageDbContext>();
        DbContext.Database.EnsureCreated();
    }

    /// <summary>
    /// 配置测试服务
    /// </summary>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // 添加内存数据库
        services.AddDbContext<MedicalImageDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        // 添加日志
        services.AddLogging(builder => builder.AddConsole());

        // 添加仓储和工作单元
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
    }

    /// <summary>
    /// 创建测试数据
    /// </summary>
    protected T CreateTestEntity<T>() where T : class
    {
        return Fixture.Create<T>();
    }

    /// <summary>
    /// 创建测试数据列表
    /// </summary>
    protected List<T> CreateTestEntities<T>(int count = 3) where T : class
    {
        return Fixture.CreateMany<T>(count).ToList();
    }

    /// <summary>
    /// 验证Mock调用
    /// </summary>
    protected void VerifyMockCalls<T>(Mock<T> mock, Times times) where T : class
    {
        mock.VerifyAll();
    }

    public virtual void Dispose()
    {
        DbContext?.Dispose();
        ServiceProvider?.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 异步测试基类
/// </summary>
public abstract class AsyncTestBase : TestBase, IAsyncDisposable
{
    public virtual async ValueTask DisposeAsync()
    {
        if (DbContext != null)
        {
            await DbContext.DisposeAsync();
        }
        
        ServiceProvider?.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 数据库测试基类
/// </summary>
public abstract class DatabaseTestBase : AsyncTestBase
{
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // 可以在这里添加特定的数据库测试配置
    }

    /// <summary>
    /// 清理数据库
    /// </summary>
    protected async Task ClearDatabaseAsync()
    {
        var entityTypes = DbContext.Model.GetEntityTypes();
        
        foreach (var entityType in entityTypes)
        {
            var tableName = entityType.GetTableName();
            if (!string.IsNullOrEmpty(tableName))
            {
                await DbContext.Database.ExecuteSqlRawAsync($"DELETE FROM [{tableName}]");
            }
        }
        
        await DbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 种子测试数据
    /// </summary>
    protected virtual async Task SeedTestDataAsync()
    {
        // 子类可以重写此方法来添加特定的测试数据
        await Task.CompletedTask;
    }
}

/// <summary>
/// AutoData属性，用于自动生成测试数据
/// </summary>
public class AutoMoqDataAttribute : AutoDataAttribute
{
    public AutoMoqDataAttribute() : base(() =>
    {
        var fixture = new Fixture();
        fixture.Customize(new AutoMoqCustomization { ConfigureMembers = true });
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        return fixture;
    })
    {
    }
}

/// <summary>
/// 内联AutoData属性
/// </summary>
public class InlineAutoMoqDataAttribute : InlineAutoDataAttribute
{
    public InlineAutoMoqDataAttribute(params object[] values) : base(new AutoMoqDataAttribute(), values)
    {
    }
}

/// <summary>
/// 测试工具类
/// </summary>
public static class TestHelpers
{
    /// <summary>
    /// 创建测试用的DICOM文件路径
    /// </summary>
    public static string CreateTestDicomFile()
    {
        var tempPath = Path.GetTempFileName();
        File.WriteAllBytes(tempPath, new byte[] { 0x44, 0x49, 0x43, 0x4D }); // "DICM"
        return tempPath;
    }

    /// <summary>
    /// 创建测试用的图像文件
    /// </summary>
    public static string CreateTestImageFile(string extension = ".jpg")
    {
        var tempPath = Path.ChangeExtension(Path.GetTempFileName(), extension);
        
        // 创建一个简单的测试图像数据
        var imageData = new byte[1024];
        new Random().NextBytes(imageData);
        File.WriteAllBytes(tempPath, imageData);
        
        return tempPath;
    }

    /// <summary>
    /// 创建测试用的JSON文件
    /// </summary>
    public static string CreateTestJsonFile(object data)
    {
        var tempPath = Path.ChangeExtension(Path.GetTempFileName(), ".json");
        var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true
        });
        File.WriteAllText(tempPath, json);
        return tempPath;
    }

    /// <summary>
    /// 等待异步操作完成
    /// </summary>
    public static async Task WaitForAsync(Func<bool> condition, TimeSpan timeout)
    {
        var endTime = DateTime.UtcNow.Add(timeout);
        
        while (DateTime.UtcNow < endTime)
        {
            if (condition())
                return;
                
            await Task.Delay(100);
        }
        
        throw new TimeoutException($"条件在 {timeout} 内未满足");
    }

    /// <summary>
    /// 清理临时文件
    /// </summary>
    public static void CleanupTempFiles(params string[] filePaths)
    {
        foreach (var filePath in filePaths)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch
            {
                // 忽略清理错误
            }
        }
    }
}

/// <summary>
/// 测试数据生成器
/// </summary>
public static class TestDataGenerator
{
    private static readonly Random Random = new();

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    public static string GenerateRandomString(int length = 10)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// 生成随机邮箱
    /// </summary>
    public static string GenerateRandomEmail()
    {
        return $"{GenerateRandomString(8)}@{GenerateRandomString(5)}.com";
    }

    /// <summary>
    /// 生成随机日期
    /// </summary>
    public static DateTime GenerateRandomDate(DateTime? minDate = null, DateTime? maxDate = null)
    {
        var min = minDate ?? DateTime.Now.AddYears(-10);
        var max = maxDate ?? DateTime.Now;
        
        var range = max - min;
        var randomTimeSpan = new TimeSpan((long)(Random.NextDouble() * range.Ticks));
        
        return min + randomTimeSpan;
    }

    /// <summary>
    /// 生成随机GUID字符串
    /// </summary>
    public static string GenerateRandomGuidString()
    {
        return Guid.NewGuid().ToString();
    }
}
