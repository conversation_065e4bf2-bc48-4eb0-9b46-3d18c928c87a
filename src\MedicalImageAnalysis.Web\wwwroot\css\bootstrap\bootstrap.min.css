/* Bootstrap 5.3.0 - 简化版本，仅包含基本样式 */
/* 这是一个占位文件，实际项目中应该使用完整的 Bootstrap CSS */

:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
}

.container-fluid {
  width: 100%;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
  margin-right: auto;
  margin-left: auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.75rem;
  margin-left: -0.75rem;
}

.col-12 { flex: 0 0 auto; width: 100%; }
.col-md-3 { flex: 0 0 auto; width: 25%; }
.col-md-4 { flex: 0 0 auto; width: 33.333333%; }
.col-md-6 { flex: 0 0 auto; width: 50%; }
.col-md-8 { flex: 0 0 auto; width: 66.666667%; }
.col-lg-3 { flex: 0 0 auto; width: 25%; }

[class*="col-"] {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.375rem;
}

.card-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
  flex: 1 1 auto;
  padding: 1rem 1rem;
}

.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.375rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-success {
  color: #fff;
  background-color: var(--bs-success);
  border-color: var(--bs-success);
}

.btn-info {
  color: #000;
  background-color: var(--bs-info);
  border-color: var(--bs-info);
}

.btn-warning {
  color: #000;
  background-color: var(--bs-warning);
  border-color: var(--bs-warning);
}

.btn-outline-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-secondary {
  color: var(--bs-secondary);
  border-color: var(--bs-secondary);
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
}

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb;
}

.text-primary { color: var(--bs-primary) !important; }
.text-success { color: var(--bs-success) !important; }
.text-warning { color: var(--bs-warning) !important; }
.text-muted { color: #6c757d !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }

.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }

.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }

.text-center { text-align: center !important; }
.text-end { text-align: right !important; }

.h-100 { height: 100% !important; }

.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }

.border-0 { border: 0 !important; }

.bg-primary { background-color: var(--bs-primary) !important; }
.bg-gradient { background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)) !important; }

.text-white { color: #fff !important; }

.rounded-circle { border-radius: 50% !important; }

.mx-auto { margin-right: auto !important; margin-left: auto !important; }

.gap-2 { gap: 0.5rem !important; }

.lead { font-size: 1.25rem; font-weight: 300; }

.display-1 { font-size: calc(1.625rem + 4.5vw); font-weight: 300; line-height: 1.2; }

.opacity-50 { opacity: 0.5 !important; }

.list-unstyled { padding-left: 0; list-style: none; }

.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
}

.bg-success { background-color: var(--bs-success) !important; }

.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
