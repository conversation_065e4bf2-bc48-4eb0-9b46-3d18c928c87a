﻿#pragma checksum "..\..\..\..\Views\DirectoryView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DB5089783D9A3E0892382DA72A891EF0A091920E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// DirectoryView
    /// </summary>
    public partial class DirectoryView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 66 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView DirectoryTreeView;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentPathText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView FileListView;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileCountText;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeText;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card FilePreviewCard;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image FilePreviewImage;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card FileInfoCard;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FileInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileTypeText;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileCreatedText;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileModifiedText;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilePathText;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card DicomInfoCard;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DicomInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PatientNameText;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PatientIdText;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StudyDateText;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModalityText;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\DirectoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SeriesDescText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/directoryview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DirectoryView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 59 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshDirectory_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DirectoryTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 69 "..\..\..\..\Views\DirectoryView.xaml"
            this.DirectoryTreeView.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.DirectoryTreeView_SelectedItemChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 105 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateFolder_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 118 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UploadFiles_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 131 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSelected_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CurrentPathText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FileListView = ((System.Windows.Controls.ListView)(target));
            
            #line 163 "..\..\..\..\Views\DirectoryView.xaml"
            this.FileListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FileListView_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 164 "..\..\..\..\Views\DirectoryView.xaml"
            this.FileListView.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.FileListView_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FileCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.FilePreviewCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 11:
            this.FilePreviewImage = ((System.Windows.Controls.Image)(target));
            return;
            case 12:
            this.FileInfoCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 13:
            this.FileInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.FileTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.FileCreatedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.FileModifiedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.FilePathText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.DicomInfoCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 21:
            this.DicomInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.PatientNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.PatientIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.StudyDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.ModalityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.SeriesDescText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            
            #line 317 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFile_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 321 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyPath_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 325 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RenameFile_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 329 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MoveFile_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 333 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFile_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 340 "..\..\..\..\Views\DirectoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowProperties_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

