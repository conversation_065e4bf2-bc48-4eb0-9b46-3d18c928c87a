@page "/test"

<PageTitle>测试页面</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>测试页面</h3>
                </div>
                <div class="card-body">
                    <h4>基本功能测试</h4>
                    <p>如果您能看到这个页面，说明Web界面基本功能正常。</p>
                    
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        Web服务运行正常！
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Bootstrap 样式测试</h5>
                            <button class="btn btn-primary me-2">主要按钮</button>
                            <button class="btn btn-secondary me-2">次要按钮</button>
                            <button class="btn btn-success">成功按钮</button>
                        </div>
                        <div class="col-md-6">
                            <h5>FontAwesome 图标测试</h5>
                            <i class="fas fa-home fa-2x me-3"></i>
                            <i class="fas fa-user fa-2x me-3"></i>
                            <i class="fas fa-cog fa-2x me-3"></i>
                            <i class="fas fa-heart fa-2x"></i>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h5>JavaScript 交互测试</h5>
                            <button class="btn btn-info" @onclick="ShowAlert">点击测试 JavaScript</button>
                            <p class="mt-2">点击次数: @clickCount</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private int clickCount = 0;

    private async Task ShowAlert()
    {
        clickCount++;
        await JSRuntime.InvokeVoidAsync("alert", $"测试成功！这是第 {clickCount} 次点击。");
    }
}
