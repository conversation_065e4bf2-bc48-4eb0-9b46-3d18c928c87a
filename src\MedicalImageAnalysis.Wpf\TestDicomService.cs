using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Data;
using MedicalImageAnalysis.Core.Services;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Wpf;

/// <summary>
/// DICOM服务测试类
/// </summary>
public static class TestDicomService
{
    /// <summary>
    /// 测试DICOM服务功能
    /// </summary>
    public static async Task TestAsync()
    {
        try
        {
            Console.WriteLine("开始测试DICOM服务...");

            // 创建数据库上下文
            var options = new DbContextOptionsBuilder<MedicalImageDbContext>()
                .UseSqlite("Data Source=test_medical_images.db")
                .Options;

            using var dbContext = new MedicalImageDbContext(options);
            
            // 创建服务
            var logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<DatabaseService>.Instance;
            var databaseService = new DatabaseService(dbContext, logger);
            
            var dicomLogger = Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleDicomService>.Instance;
            var dicomService = new SimpleDicomService(dicomLogger, databaseService);

            // 确保数据库存在
            await databaseService.CreateDatabaseAsync();
            Console.WriteLine("数据库创建成功");

            // 测试创建患者
            var patient = new Core.Entities.Patient
            {
                PatientId = "TEST001",
                PatientName = "测试患者",
                PatientSex = "M",
                PatientBirthDate = DateTime.Now.AddYears(-30)
            };
            
            patient = await databaseService.CreatePatientAsync(patient);
            Console.WriteLine($"创建患者成功: {patient.PatientName}");

            // 测试创建研究
            var study = new Core.Entities.DicomStudy
            {
                StudyInstanceUid = Guid.NewGuid().ToString(),
                StudyId = "STUDY001",
                StudyDescription = "测试研究",
                StudyDateTime = DateTime.Now,
                PatientId = patient.Id
            };
            
            study = await databaseService.CreateStudyAsync(study);
            Console.WriteLine($"创建研究成功: {study.StudyDescription}");

            // 测试创建序列
            var series = new Core.Entities.DicomSeries
            {
                SeriesInstanceUid = Guid.NewGuid().ToString(),
                SeriesNumber = 1,
                SeriesDescription = "测试序列",
                Modality = "CT",
                SeriesDateTime = DateTime.Now,
                StudyId = study.Id
            };
            
            series = await databaseService.CreateSeriesAsync(series);
            Console.WriteLine($"创建序列成功: {series.SeriesDescription}");

            // 测试创建实例
            var instance = new Core.Entities.DicomInstance
            {
                SopInstanceUid = Guid.NewGuid().ToString(),
                InstanceNumber = 1,
                Rows = 512,
                Columns = 512,
                FilePath = "test.dcm",
                FileSize = 1024,
                FileHash = "testhash",
                SeriesId = series.Id
            };
            
            instance = await databaseService.CreateInstanceAsync(instance);
            Console.WriteLine($"创建实例成功: {instance.SopInstanceUid}");

            // 测试创建标注
            var annotation = new Core.Entities.Annotation
            {
                Label = "测试标注",
                Description = "这是一个测试标注",
                Type = Core.Entities.AnnotationType.BoundingBox,
                Source = Core.Entities.AnnotationSource.Manual,
                Confidence = 1.0,
                BoundingBox = new Core.Entities.BoundingBox
                {
                    CenterX = 0.5,
                    CenterY = 0.5,
                    Width = 0.2,
                    Height = 0.2
                },
                InstanceId = instance.Id,
                CreatedBy = "测试用户"
            };
            
            annotation = await databaseService.CreateAnnotationAsync(annotation);
            Console.WriteLine($"创建标注成功: {annotation.Label}");

            // 测试查询功能
            var patientCount = await databaseService.GetPatientCountAsync();
            var studyCount = await databaseService.GetStudyCountAsync();
            var seriesCount = await databaseService.GetSeriesCountAsync();
            var instanceCount = await databaseService.GetInstanceCountAsync();
            var annotationCount = await databaseService.GetAnnotationCountAsync();

            Console.WriteLine($"统计信息:");
            Console.WriteLine($"  患者数量: {patientCount}");
            Console.WriteLine($"  研究数量: {studyCount}");
            Console.WriteLine($"  序列数量: {seriesCount}");
            Console.WriteLine($"  实例数量: {instanceCount}");
            Console.WriteLine($"  标注数量: {annotationCount}");

            // 测试DICOM服务
            Console.WriteLine("\n测试DICOM服务功能...");
            
            // 创建一个测试文件
            var testFilePath = "test_dicom.dcm";
            await File.WriteAllBytesAsync(testFilePath, new byte[] { 0x44, 0x49, 0x43, 0x4D }); // "DICM"

            try
            {
                var validationResult = await dicomService.ValidateDicomFileAsync(testFilePath);
                Console.WriteLine($"DICOM验证结果: {validationResult.IsValid}");

                if (validationResult.IsValid)
                {
                    var metadata = await dicomService.ExtractMetadataAsync(testFilePath);
                    if (metadata != null)
                    {
                        Console.WriteLine($"提取元数据成功: 患者={metadata.PatientName}, 模态={metadata.Modality}");
                    }

                    var coordinateInfo = await dicomService.GetImageCoordinateInfoAsync(testFilePath);
                    if (coordinateInfo != null)
                    {
                        Console.WriteLine($"坐标信息: 图像尺寸={coordinateInfo.Rows}x{coordinateInfo.Columns}");
                        Console.WriteLine($"像素间距: {coordinateInfo.PixelSpacing.Row}x{coordinateInfo.PixelSpacing.Column}");
                        
                        // 测试坐标转换
                        var patientCoord = coordinateInfo.PixelToPatientCoordinate(100, 100);
                        Console.WriteLine($"像素(100,100)对应患者坐标: ({patientCoord.X:F2}, {patientCoord.Y:F2}, {patientCoord.Z:F2})");
                        
                        var pixelCoord = coordinateInfo.PatientToPixelCoordinate(patientCoord.X, patientCoord.Y, patientCoord.Z);
                        Console.WriteLine($"患者坐标转回像素坐标: ({pixelCoord.Row}, {pixelCoord.Column})");
                        
                        // 测试距离计算
                        var distance = coordinateInfo.CalculatePixelDistance(0, 0, 100, 100);
                        Console.WriteLine($"像素(0,0)到(100,100)的物理距离: {distance:F2}mm");
                        
                        // 测试面积计算
                        var area = coordinateInfo.CalculatePixelArea(100);
                        Console.WriteLine($"100个像素的物理面积: {area:F2}mm²");
                    }
                }
            }
            finally
            {
                // 清理测试文件
                if (File.Exists(testFilePath))
                {
                    File.Delete(testFilePath);
                }
            }

            Console.WriteLine("\n所有测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }
}
