2025-07-23 08:31:47.788 +08:00 [INF] 启动医学影像解析系统 API
2025-07-23 08:31:47.895 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-23 08:31:47.902 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:31:47.903 +08:00 [INF] Hosting environment: Development
2025-07-23 08:31:47.905 +08:00 [INF] Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api
2025-07-23 08:32:39.529 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-23 08:33:00.599 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data
2025-07-23 08:33:00.603 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\logs
2025-07-23 08:33:00.607 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\temp
2025-07-23 08:33:00.610 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\output
2025-07-23 08:33:00.614 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\models
2025-07-23 08:33:00.618 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\config
2025-07-23 08:33:00.622 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\backup
2025-07-23 08:33:00.625 +08:00 [INF] 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\cache
[2025-07-23 08:52:59.520 +08:00 INF] : 启动医学影像解析系统 API {}
[2025-07-23 08:52:59.615 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5000 {"EventId":{"Id":14,"Name":"ListeningOnAddress"}}
[2025-07-23 08:52:59.621 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down. {}
[2025-07-23 08:52:59.623 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Development {}
[2025-07-23 08:52:59.625 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api {}
[2025-07-23 08:53:56.796 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"RequestId":"0HNE9GDJKQ44P:00000001","RequestPath":"/health","ConnectionId":"0HNE9GDJKQ44P"}
