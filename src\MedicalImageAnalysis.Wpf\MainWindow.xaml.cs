﻿using System;
using System.Windows;
using System.Windows.Controls;
using MedicalImageAnalysis.Wpf.Views;
using Microsoft.Extensions.Logging;
using Serilog;

// WPF 类型别名已在 GlobalUsings.cs 中定义

namespace MedicalImageAnalysis.Wpf;

/// <summary>
/// 主窗口 - 医学影像解析系统
/// </summary>
public partial class MainWindow : Window
{
    private readonly ILogger<MainWindow> _logger;

    public MainWindow()
    {
        InitializeComponent();

        // 创建一个简单的日志记录器
        var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
        _logger = loggerFactory.CreateLogger<MainWindow>();

        // 设置默认页面
        NavigationListBox.SelectedIndex = 0;

        // 显示一个简单的欢迎消息，不加载复杂的HomeView
        MainContentControl.Content = new TextBlock
        {
            Text = "欢迎使用医学影像解析系统\n\n系统已成功启动！",
            FontSize = 24,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            TextAlignment = TextAlignment.Center
        };

        _logger.LogInformation("医学影像解析系统主窗口已初始化");

        // 运行测试（仅在调试模式下）
        #if DEBUG
        RunTestAsync();
        #endif
    }

    /// <summary>
    /// 运行测试
    /// </summary>
    private async void RunTestAsync()
    {
        try
        {
            await TestDicomService.TestAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "运行测试时发生错误");
        }
    }

    /// <summary>
    /// 导航菜单选择变化事件
    /// </summary>
    private void NavigationListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (NavigationListBox.SelectedItem == null) return;

        var selectedItem = NavigationListBox.SelectedItem as ListBoxItem;
        var itemName = selectedItem?.Name;

        try
        {
            switch (itemName)
            {
                case "HomeMenuItem":
                    ShowHomePage();
                    UpdateStatus("首页");
                    break;
                case "DicomUploadMenuItem":
                    ShowDicomUploadPage();
                    UpdateStatus("DICOM 文件上传");
                    break;
                case "ImageProcessingMenuItem":
                    ShowImageProcessingPage();
                    UpdateStatus("影像处理");
                    break;
                case "AnnotationMenuItem":
                    ShowAnnotationPage();
                    UpdateStatus("智能标注");
                    break;
                case "ModelTrainingMenuItem":
                    ShowModelTrainingPage();
                    UpdateStatus("模型训练");
                    break;
                case "StatisticsMenuItem":
                    ShowStatisticsPage();
                    UpdateStatus("统计分析");
                    break;
                case "DirectoryMenuItem":
                    ShowDirectoryPage();
                    UpdateStatus("目录管理");
                    break;
                default:
                    ShowHomePage();
                    UpdateStatus("首页");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导航到页面时发生错误: {ItemName}", itemName);
            UpdateStatus($"错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 显示首页
    /// </summary>
    private void ShowHomePage()
    {
        try
        {
            MainContentControl.Content = new HomeView();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建HomeView时发生错误");
            MainContentControl.Content = new TextBlock
            {
                Text = $"首页加载失败: {ex.Message}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(20)
            };
        }
    }

    /// <summary>
    /// 显示 DICOM 上传页面
    /// </summary>
    private void ShowDicomUploadPage()
    {
        MainContentControl.Content = new DicomUploadView();
    }

    /// <summary>
    /// 显示影像处理页面
    /// </summary>
    private void ShowImageProcessingPage()
    {
        MainContentControl.Content = new ImageProcessingView();
    }

    /// <summary>
    /// 显示智能标注页面
    /// </summary>
    private void ShowAnnotationPage()
    {
        MainContentControl.Content = new AnnotationView();
    }

    /// <summary>
    /// 显示模型训练页面
    /// </summary>
    private void ShowModelTrainingPage()
    {
        MainContentControl.Content = new ModelTrainingView();
    }

    /// <summary>
    /// 显示统计分析页面
    /// </summary>
    private void ShowStatisticsPage()
    {
        MainContentControl.Content = new StatisticsView();
    }

    /// <summary>
    /// 显示目录管理页面
    /// </summary>
    private void ShowDirectoryPage()
    {
        MainContentControl.Content = new DirectoryView();
    }

    /// <summary>
    /// 更新状态栏
    /// </summary>
    private void UpdateStatus(string status)
    {
        StatusTextBlock.Text = $"当前页面: {status}";
    }

    /// <summary>
    /// 设置按钮点击事件
    /// </summary>
    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var settingsWindow = new SettingsWindow
            {
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            settingsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开设置窗口时发生错误");
            MessageBox.Show($"无法打开设置窗口：{ex.Message}", "错误",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 帮助按钮点击事件
    /// </summary>
    private void HelpButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var helpWindow = new HelpWindow
            {
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            helpWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开帮助窗口时发生错误");
            MessageBox.Show($"无法打开帮助窗口：{ex.Message}", "错误",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}