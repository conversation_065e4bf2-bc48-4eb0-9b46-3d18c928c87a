@page "/model-training"
@using MedicalImageAnalysis.Core.Entities
@using MedicalImageAnalysis.Core.Interfaces
@using Microsoft.AspNetCore.SignalR.Client
@inject IJSRuntime JSRuntime
@inject IYoloService YoloService
@inject NavigationManager Navigation
@implements IAsyncDisposable

<PageTitle>模型训练 - 医学影像解析系统</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-cogs me-2 text-warning"></i>
                    YOLOv11 模型训练
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/">首页</a></li>
                        <li class="breadcrumb-item active">模型训练</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- 训练配置 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        训练配置
                    </h5>
                </div>
                <div class="card-body">
                    <EditForm Model="@trainingConfig" OnValidSubmit="@StartTraining">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="text-danger" />

                        <div class="mb-3">
                            <label class="form-label">实验名称</label>
                            <InputText @bind-Value="trainingConfig.ExperimentName" class="form-control" placeholder="输入实验名称" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">数据集路径</label>
                            <InputText @bind-Value="trainingConfig.DatasetPath" class="form-control" placeholder="数据集路径" />
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">训练轮数</label>
                                    <InputNumber @bind-Value="trainingConfig.Epochs" class="form-control" min="1" max="1000" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">批次大小</label>
                                    <InputNumber @bind-Value="trainingConfig.BatchSize" class="form-control" min="1" max="64" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">学习率</label>
                                    <InputNumber @bind-Value="trainingConfig.LearningRate" class="form-control" step="0.001" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">图像尺寸</label>
                                    <InputNumber @bind-Value="trainingConfig.ImageSize" class="form-control" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">模型类型</label>
                            <InputSelect @bind-Value="trainingConfig.ModelType" class="form-select">
                                <option value="yolo11n">YOLOv11n (轻量级)</option>
                                <option value="yolo11s">YOLOv11s (小型)</option>
                                <option value="yolo11m">YOLOv11m (中型)</option>
                                <option value="yolo11l">YOLOv11l (大型)</option>
                                <option value="yolo11x">YOLOv11x (超大型)</option>
                            </InputSelect>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <InputCheckbox @bind-Value="trainingConfig.UsePretrainedWeights" class="form-check-input" id="usePretrainedWeights" />
                                <label class="form-check-label" for="usePretrainedWeights">
                                    使用预训练权重
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" disabled="@isTraining">
                                @if (isTraining)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>训练中...</span>
                                }
                                else
                                {
                                    <i class="fas fa-rocket me-2"></i>
                                    <span>开始训练</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        训练进度
                    </h5>
                </div>
                <div class="card-body">
                    @if (isTraining)
                    {
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>总体进度</span>
                                <span class="badge bg-primary">@($"{trainingProgress.CurrentEpoch}/{trainingProgress.TotalEpochs}")</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     style="width: @(trainingProgress.OverallProgress)%">
                                    @($"{trainingProgress.OverallProgress:F1}%")
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>当前轮次进度</span>
                                <span class="badge bg-info">@($"{trainingProgress.CurrentBatch}/{trainingProgress.TotalBatches}")</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-info"
                                     role="progressbar"
                                     style="width: @(trainingProgress.EpochProgress)%">
                                    @($"{trainingProgress.EpochProgress:F1}%")
                                </div>
                            </div>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-value text-success">@($"{trainingProgress.CurrentLoss:F4}")</div>
                                    <div class="metric-label">当前损失</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric-item">
                                    <div class="metric-value text-info">@($"{trainingProgress.LearningRate:F6}")</div>
                                    <div class="metric-label">学习率</div>
                                </div>
                            </div>
                        </div>

                        <div class="row text-center mt-3">
                            <div class="col-4">
                                <div class="metric-item">
                                    <div class="metric-value text-warning">@($"{trainingProgress.mAP50:F3}")</div>
                                    <div class="metric-label">mAP@0.5</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="metric-item">
                                    <div class="metric-value text-primary">@($"{trainingProgress.Precision:F3}")</div>
                                    <div class="metric-label">精确度</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="metric-item">
                                    <div class="metric-value text-danger">@($"{trainingProgress.Recall:F3}")</div>
                                    <div class="metric-label">召回率</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                已用时间: @GetElapsedTime() |
                                预计剩余: @GetEstimatedTimeRemaining()
                            </small>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-play-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">配置训练参数并点击"开始训练"</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 训练历史 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        训练历史
                    </h5>
                </div>
                <div class="card-body">
                    @if (trainingHistory.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>实验名称</th>
                                        <th>模型类型</th>
                                        <th>训练时间</th>
                                        <th>最佳mAP</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var history in trainingHistory)
                                    {
                                        <tr>
                                            <td>@history.ExperimentName</td>
                                            <td>
                                                <span class="badge bg-secondary">@history.ModelType</span>
                                            </td>
                                            <td>@history.TrainingTime.ToString("yyyy-MM-dd HH:mm")</td>
                                            <td>@($"{history.BestmAP:F3}")</td>
                                            <td>
                                                @if (history.Status == "completed")
                                                {
                                                    <span class="badge bg-success">已完成</span>
                                                }
                                                else if (history.Status == "running")
                                                {
                                                    <span class="badge bg-primary">运行中</span>
                                                }
                                                else if (history.Status == "failed")
                                                {
                                                    <span class="badge bg-danger">失败</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">未知</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" @onclick="() => ViewTrainingDetails(history.Id)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" @onclick="() => DownloadModel(history.Id)">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" @onclick="() => DeleteTraining(history.Id)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">暂无训练历史记录</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .metric-item {
        padding: 0.5rem 0;
    }

    .metric-value {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }

    .metric-label {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .progress {
        height: 8px;
    }

    .card {
        transition: transform 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
    }
</style>

@code {
    private YoloTrainingConfig trainingConfig = new();
    private TrainingProgress trainingProgress = new();
    private List<TrainingHistoryItem> trainingHistory = new();
    private bool isTraining = false;
    private DateTime trainingStartTime;
    private HubConnection? hubConnection;

    protected override async Task OnInitializedAsync()
    {
        // 初始化训练配置
        trainingConfig = new YoloTrainingConfig
        {
            ExperimentName = $"experiment_{DateTime.Now:yyyyMMdd_HHmmss}",
            DatasetPath = "dataset",
            OutputDirectory = "training_output",
            Epochs = 100,
            BatchSize = 16,
            LearningRate = 0.01,
            ImageSize = 640,
            ModelType = "yolo11n",
            UsePretrainedWeights = true
        };

        // 初始化SignalR连接
        hubConnection = new HubConnectionBuilder()
            .WithUrl(Navigation.ToAbsoluteUri("/processingHub"))
            .Build();

        hubConnection.On<TrainingProgress>("TrainingProgress", (progress) =>
        {
            trainingProgress = progress;
            InvokeAsync(StateHasChanged);
        });

        hubConnection.On<string>("TrainingCompleted", (message) =>
        {
            isTraining = false;
            InvokeAsync(StateHasChanged);
            InvokeAsync(() => JSRuntime.InvokeVoidAsync("blazorHelpers.showSuccess", "训练完成！"));
        });

        hubConnection.On<string>("TrainingFailed", (error) =>
        {
            isTraining = false;
            InvokeAsync(StateHasChanged);
            InvokeAsync(() => JSRuntime.InvokeVoidAsync("blazorHelpers.showError", $"训练失败: {error}"));
        });

        await hubConnection.StartAsync();

        // 加载训练历史
        await LoadTrainingHistory();
    }

    private async Task StartTraining()
    {
        try
        {
            isTraining = true;
            trainingStartTime = DateTime.Now;
            trainingProgress = new TrainingProgress
            {
                TotalEpochs = trainingConfig.Epochs,
                CurrentEpoch = 0,
                OverallProgress = 0,
                EpochProgress = 0
            };

            StateHasChanged();

            // 创建进度回调
            var progress = new Progress<TrainingProgress>(p =>
            {
                trainingProgress = p;
                InvokeAsync(StateHasChanged);
            });

            // 开始训练
            var result = await YoloService.TrainModelAsync(trainingConfig, progress);

            if (result.Success)
            {
                await JSRuntime.InvokeVoidAsync("blazorHelpers.showSuccess", "模型训练成功完成！");
                await LoadTrainingHistory();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("blazorHelpers.showError", $"训练失败: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("blazorHelpers.showError", $"训练过程中发生错误: {ex.Message}");
        }
        finally
        {
            isTraining = false;
            StateHasChanged();
        }
    }

    private async Task LoadTrainingHistory()
    {
        // 这里应该从数据库或文件系统加载训练历史
        // 暂时使用模拟数据
        trainingHistory = new List<TrainingHistoryItem>
        {
            new TrainingHistoryItem
            {
                Id = Guid.NewGuid(),
                ExperimentName = "chest_xray_detection",
                ModelType = "yolo11n",
                TrainingTime = DateTime.Now.AddDays(-2),
                BestmAP = 0.856,
                Status = "completed"
            },
            new TrainingHistoryItem
            {
                Id = Guid.NewGuid(),
                ExperimentName = "lung_nodule_detection",
                ModelType = "yolo11s",
                TrainingTime = DateTime.Now.AddDays(-5),
                BestmAP = 0.742,
                Status = "completed"
            }
        };
    }

    private async Task ViewTrainingDetails(Guid trainingId)
    {
        await JSRuntime.InvokeVoidAsync("blazorHelpers.showInfo", "查看训练详情功能正在开发中");
    }

    private async Task DownloadModel(Guid trainingId)
    {
        await JSRuntime.InvokeVoidAsync("blazorHelpers.showInfo", "模型下载功能正在开发中");
    }

    private async Task DeleteTraining(Guid trainingId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "确定要删除这个训练记录吗？");
        if (confirmed)
        {
            trainingHistory.RemoveAll(h => h.Id == trainingId);
            StateHasChanged();
            await JSRuntime.InvokeVoidAsync("blazorHelpers.showSuccess", "训练记录已删除");
        }
    }

    private string GetElapsedTime()
    {
        if (!isTraining) return "00:00:00";

        var elapsed = DateTime.Now - trainingStartTime;
        return $"{elapsed.Hours:D2}:{elapsed.Minutes:D2}:{elapsed.Seconds:D2}";
    }

    private string GetEstimatedTimeRemaining()
    {
        if (!isTraining || trainingProgress.OverallProgress <= 0) return "计算中...";

        var elapsed = DateTime.Now - trainingStartTime;
        var totalEstimated = elapsed.TotalSeconds / (trainingProgress.OverallProgress / 100.0);
        var remaining = TimeSpan.FromSeconds(totalEstimated - elapsed.TotalSeconds);

        if (remaining.TotalSeconds < 0) return "即将完成";

        return $"{remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2}";
    }

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    // 数据模型
    public class TrainingProgress
    {
        public int TotalEpochs { get; set; }
        public int CurrentEpoch { get; set; }
        public int TotalBatches { get; set; }
        public int CurrentBatch { get; set; }
        public double OverallProgress { get; set; }
        public double EpochProgress { get; set; }
        public double CurrentLoss { get; set; }
        public double LearningRate { get; set; }
        public double mAP50 { get; set; }
        public double Precision { get; set; }
        public double Recall { get; set; }
    }

    public class TrainingHistoryItem
    {
        public Guid Id { get; set; }
        public string ExperimentName { get; set; } = "";
        public string ModelType { get; set; } = "";
        public DateTime TrainingTime { get; set; }
        public double BestmAP { get; set; }
        public string Status { get; set; } = "";
    }
}
