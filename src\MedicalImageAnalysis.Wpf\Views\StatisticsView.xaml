<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.StatisticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="24">
            <!-- 标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="ChartLine"
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                <TextBlock Text="统计分析中心"
                         FontSize="28"
                         FontWeight="Medium"
                         VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 概览统计卡片 -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 总处理数量 -->
                <materialDesign:Card Grid.Column="0"
                                   Margin="0,0,8,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileImage"
                                               Width="32" Height="32"
                                               Foreground="#2196F3"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                        <TextBlock x:Name="TotalProcessedText"
                                 Text="1,247"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="总处理数量"
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 成功率 -->
                <materialDesign:Card Grid.Column="1"
                                   Margin="8,0,8,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CheckCircle"
                                               Width="32" Height="32"
                                               Foreground="#4CAF50"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                        <TextBlock x:Name="SuccessRateText"
                                 Text="94.2%"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="成功率"
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 平均处理时间 -->
                <materialDesign:Card Grid.Column="2"
                                   Margin="8,0,8,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Timer"
                                               Width="32" Height="32"
                                               Foreground="#FF9800"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                        <TextBlock x:Name="AvgProcessingTimeText"
                                 Text="2.3s"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="平均处理时间"
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 活跃用户 -->
                <materialDesign:Card Grid.Column="3"
                                   Margin="8,0,0,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Account"
                                               Width="32" Height="32"
                                               Foreground="#9C27B0"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                        <TextBlock x:Name="ActiveUsersText"
                                 Text="23"
                                 FontSize="24"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="活跃用户"
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：图表和趋势 -->
                <StackPanel Grid.Column="0" Margin="0,0,12,0">
                    <!-- 处理趋势图表 -->
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                        <StackPanel Margin="24">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <TextBlock Text="处理趋势分析"
                                         FontSize="18"
                                         FontWeight="Medium"
                                         VerticalAlignment="Center"/>
                                <ComboBox x:Name="TrendPeriodComboBox"
                                        SelectedIndex="0"
                                        Margin="16,0,0,0"
                                        Width="120"
                                        SelectionChanged="TrendPeriodComboBox_SelectionChanged">
                                    <ComboBoxItem Content="最近7天"/>
                                    <ComboBoxItem Content="最近30天"/>
                                    <ComboBoxItem Content="最近90天"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- 简化的图表显示区域 -->
                            <Border Height="200"
                                  Background="{DynamicResource MaterialDesignCardBackground}"
                                  BorderBrush="{DynamicResource MaterialDesignDivider}"
                                  BorderThickness="1">
                                <Canvas x:Name="TrendChart" Background="Transparent">
                                    <!-- 图表将通过代码绘制 -->
                                </Canvas>
                            </Border>

                            <StackPanel Orientation="Horizontal"
                                      HorizontalAlignment="Center"
                                      Margin="0,16,0,0">
                                <StackPanel Orientation="Horizontal" Margin="0,0,16,0">
                                    <Rectangle Width="12" Height="12" Fill="#2196F3" Margin="0,0,4,0"/>
                                    <TextBlock Text="成功处理" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,16,0">
                                    <Rectangle Width="12" Height="12" Fill="#F44336" Margin="0,0,4,0"/>
                                    <TextBlock Text="处理失败" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="12" Height="12" Fill="#FF9800" Margin="0,0,4,0"/>
                                    <TextBlock Text="处理中" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 文件类型分布 -->
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel Margin="24">
                            <TextBlock Text="文件类型分布"
                                     FontSize="18"
                                     FontWeight="Medium"
                                     Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 文件类型列表 -->
                                <StackPanel Grid.Column="0">
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <Rectangle Width="16" Height="16" Fill="#2196F3" Margin="0,0,8,0"/>
                                        <TextBlock Text="DICOM (.dcm)" Width="100"/>
                                        <TextBlock Text="68.5%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <Rectangle Width="16" Height="16" Fill="#4CAF50" Margin="0,0,8,0"/>
                                        <TextBlock Text="PNG" Width="100"/>
                                        <TextBlock Text="18.2%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <Rectangle Width="16" Height="16" Fill="#FF9800" Margin="0,0,8,0"/>
                                        <TextBlock Text="JPEG" Width="100"/>
                                        <TextBlock Text="10.1%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,4">
                                        <Rectangle Width="16" Height="16" Fill="#9C27B0" Margin="0,0,8,0"/>
                                        <TextBlock Text="其他" Width="100"/>
                                        <TextBlock Text="3.2%" FontWeight="Medium"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- 简化的饼图显示 -->
                                <Border Grid.Column="1"
                                      Width="120" Height="120"
                                      HorizontalAlignment="Center">
                                    <Canvas x:Name="PieChart" Background="Transparent">
                                        <!-- 饼图将通过代码绘制 -->
                                    </Canvas>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- 右侧：详细统计 -->
                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                    <!-- 系统状态 -->
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                        <StackPanel Margin="16">
                            <TextBlock Text="系统状态"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Margin="0,0,0,12"/>

                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Circle"
                                                           Width="12" Height="12"
                                                           Foreground="Green"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="API服务" Width="60"/>
                                    <TextBlock Text="正常" Foreground="Green" FontWeight="Medium"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Circle"
                                                           Width="12" Height="12"
                                                           Foreground="Green"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="数据库" Width="60"/>
                                    <TextBlock Text="正常" Foreground="Green" FontWeight="Medium"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Circle"
                                                           Width="12" Height="12"
                                                           Foreground="Orange"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="AI模型" Width="60"/>
                                    <TextBlock Text="负载高" Foreground="Orange" FontWeight="Medium"/>
                                </StackPanel>

                                <StackPanel Orientation="Horizontal" Margin="0,4">
                                    <materialDesign:PackIcon Kind="Circle"
                                                           Width="12" Height="12"
                                                           Foreground="Green"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="存储" Width="60"/>
                                    <TextBlock Text="正常" Foreground="Green" FontWeight="Medium"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 性能指标 -->
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                        <StackPanel Margin="16">
                            <TextBlock Text="性能指标"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Margin="0,0,0,12"/>

                            <StackPanel>
                                <StackPanel Margin="0,4">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="CPU使用率" Width="80"/>
                                        <TextBlock x:Name="CpuUsageText" Text="45%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <ProgressBar x:Name="CpuUsageBar"
                                               Value="45"
                                               Height="4"
                                               Margin="0,2,0,0"/>
                                </StackPanel>

                                <StackPanel Margin="0,8,0,4">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="内存使用率" Width="80"/>
                                        <TextBlock x:Name="MemoryUsageText" Text="67%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <ProgressBar x:Name="MemoryUsageBar"
                                               Value="67"
                                               Height="4"
                                               Margin="0,2,0,0"/>
                                </StackPanel>

                                <StackPanel Margin="0,8,0,4">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="磁盘使用率" Width="80"/>
                                        <TextBlock x:Name="DiskUsageText" Text="23%" FontWeight="Medium"/>
                                    </StackPanel>
                                    <ProgressBar x:Name="DiskUsageBar"
                                               Value="23"
                                               Height="4"
                                               Margin="0,2,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 最近活动 -->
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel Margin="16">
                            <TextBlock Text="最近活动"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Margin="0,0,0,12"/>

                            <ListView x:Name="RecentActivitiesListView"
                                    Height="200"
                                    ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="0,4">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="{Binding Icon}"
                                                                       Width="16" Height="16"
                                                                       Foreground="{Binding IconColor}"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Action}"
                                                         FontWeight="Medium"
                                                         TextWrapping="Wrap"/>
                                            </StackPanel>
                                            <TextBlock Text="{Binding Time}"
                                                     FontSize="11"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                     Margin="24,2,0,0"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>

            <!-- 操作按钮 -->
            <StackPanel Orientation="Horizontal"
                      HorizontalAlignment="Right"
                      Margin="0,24,0,0">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="RefreshData_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh"
                                                   Width="16" Height="16"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="刷新"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="ExportReport_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport"
                                                   Width="16" Height="16"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="导出报告"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
