using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 数据增强服务接口
/// </summary>
public interface IDataAugmentationService
{
    /// <summary>
    /// 执行数据增强
    /// </summary>
    Task<DataAugmentationResult> AugmentDataAsync(
        List<TrainingData> originalData, 
        DataAugmentationConfig config,
        IProgress<AugmentationProgress>? progressCallback = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 几何变换增强
    /// </summary>
    Task<List<TrainingData>> GeometricAugmentationAsync(
        TrainingData originalData, 
        GeometricAugmentationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 图像质量增强
    /// </summary>
    Task<List<TrainingData>> ImageQualityAugmentationAsync(
        TrainingData originalData, 
        ImageQualityAugmentationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 噪声增强
    /// </summary>
    Task<List<TrainingData>> NoiseAugmentationAsync(
        TrainingData originalData, 
        NoiseAugmentationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 医学影像特定增强
    /// </summary>
    Task<List<TrainingData>> MedicalSpecificAugmentationAsync(
        TrainingData originalData, 
        MedicalAugmentationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 混合增强策略
    /// </summary>
    Task<List<TrainingData>> MixupAugmentationAsync(
        List<TrainingData> dataList, 
        MixupConfig config,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 训练数据
/// </summary>
public class TrainingData
{
    public string ImagePath { get; set; } = "";
    public string AnnotationPath { get; set; } = "";
    public string Label { get; set; } = "";
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 数据增强配置
/// </summary>
public class DataAugmentationConfig
{
    public bool EnableGeometricAugmentation { get; set; } = true;
    public bool EnableImageQualityAugmentation { get; set; } = true;
    public bool EnableNoiseAugmentation { get; set; } = true;
    public bool EnableMedicalSpecificAugmentation { get; set; } = true;
    public bool EnableMixupAugmentation { get; set; } = false;
    
    public GeometricAugmentationConfig GeometricConfig { get; set; } = new();
    public ImageQualityAugmentationConfig ImageQualityConfig { get; set; } = new();
    public NoiseAugmentationConfig NoiseConfig { get; set; } = new();
    public MedicalAugmentationConfig MedicalConfig { get; set; } = new();
    public MixupConfig MixupConfig { get; set; } = new();
    
    public int MaxAugmentationsPerSample { get; set; } = 10;
    public double AugmentationProbability { get; set; } = 0.8;
    public bool PreserveOriginalData { get; set; } = true;
    public string OutputDirectory { get; set; } = "augmented";
}

/// <summary>
/// 几何变换增强配置
/// </summary>
public class GeometricAugmentationConfig
{
    public bool EnableRotation { get; set; } = true;
    public (double Min, double Max) RotationRange { get; set; } = (-30, 30);
    public int RotationSamples { get; set; } = 3;
    
    public bool EnableScaling { get; set; } = true;
    public (double Min, double Max) ScaleRange { get; set; } = (0.8, 1.2);
    public int ScalingSamples { get; set; } = 2;
    
    public bool EnableTranslation { get; set; } = true;
    public (double Min, double Max) TranslationRange { get; set; } = (-0.1, 0.1);
    public int TranslationSamples { get; set; } = 2;
    
    public bool EnableFlipping { get; set; } = true;
    public bool EnableHorizontalFlip { get; set; } = true;
    public bool EnableVerticalFlip { get; set; } = false;
    
    public bool EnableShearing { get; set; } = false;
    public (double Min, double Max) ShearRange { get; set; } = (-10, 10);
    public int ShearSamples { get; set; } = 1;
}

/// <summary>
/// 图像质量增强配置
/// </summary>
public class ImageQualityAugmentationConfig
{
    public bool EnableBrightnessAdjustment { get; set; } = true;
    public (double Min, double Max) BrightnessRange { get; set; } = (0.7, 1.3);
    public int BrightnessSamples { get; set; } = 2;
    
    public bool EnableContrastAdjustment { get; set; } = true;
    public (double Min, double Max) ContrastRange { get; set; } = (0.7, 1.3);
    public int ContrastSamples { get; set; } = 2;
    
    public bool EnableGammaCorrection { get; set; } = true;
    public (double Min, double Max) GammaRange { get; set; } = (0.7, 1.3);
    public int GammaSamples { get; set; } = 2;
    
    public bool EnableSaturationAdjustment { get; set; } = false;
    public (double Min, double Max) SaturationRange { get; set; } = (0.7, 1.3);
    public int SaturationSamples { get; set; } = 1;
    
    public bool EnableHueAdjustment { get; set; } = false;
    public (double Min, double Max) HueRange { get; set; } = (-10, 10);
    public int HueSamples { get; set; } = 1;
}

/// <summary>
/// 噪声增强配置
/// </summary>
public class NoiseAugmentationConfig
{
    public bool EnableGaussianNoise { get; set; } = true;
    public double GaussianNoiseStd { get; set; } = 0.02;
    public int GaussianNoiseSamples { get; set; } = 2;
    
    public bool EnableSaltPepperNoise { get; set; } = false;
    public double SaltPepperRatio { get; set; } = 0.01;
    public int SaltPepperSamples { get; set; } = 1;
    
    public bool EnableSpeckleNoise { get; set; } = false;
    public double SpeckleVariance { get; set; } = 0.01;
    public int SpeckleSamples { get; set; } = 1;
    
    public bool EnablePoissonNoise { get; set; } = false;
    public int PoissonSamples { get; set; } = 1;
}

/// <summary>
/// 医学影像特定增强配置
/// </summary>
public class MedicalAugmentationConfig
{
    public bool EnableWindowLevelAdjustment { get; set; } = true;
    public List<(double Width, double Center)> WindowLevelSettings { get; set; } = new()
    {
        (400, 40),   // 软组织窗
        (1500, -600), // 肺窗
        (80, 40),    // 脑窗
        (2000, 300)  // 骨窗
    };
    
    public bool EnableHistogramEqualization { get; set; } = false;
    public int HistogramSamples { get; set; } = 1;
    
    public bool EnableCLAHE { get; set; } = true;
    public double CLAHEClipLimit { get; set; } = 2.0;
    public (int Width, int Height) CLAHETileSize { get; set; } = (8, 8);
    public int CLAHESamples { get; set; } = 1;
    
    public bool EnableEdgeEnhancement { get; set; } = false;
    public double EdgeEnhancementStrength { get; set; } = 0.5;
    public int EdgeEnhancementSamples { get; set; } = 1;
    
    public bool EnableBlurring { get; set; } = false;
    public (double Min, double Max) BlurSigmaRange { get; set; } = (0.5, 2.0);
    public int BlurringSamples { get; set; } = 1;
}

/// <summary>
/// 混合增强配置
/// </summary>
public class MixupConfig
{
    public double Alpha { get; set; } = 0.2;
    public int MixupSamples { get; set; } = 100;
    public bool EnableLabelMixing { get; set; } = true;
    public double MixupProbability { get; set; } = 0.5;
}

/// <summary>
/// 数据增强结果
/// </summary>
public class DataAugmentationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartTime { get; set; }
    public double ProcessingTimeMs { get; set; }
    
    public int OriginalDataCount { get; set; }
    public int AugmentedDataCount { get; set; }
    public double AugmentationFactor { get; set; }
    
    public List<TrainingData> AugmentedData { get; set; } = new();
    public DataAugmentationConfig Config { get; set; } = new();
    
    public Dictionary<string, int> AugmentationTypeCount { get; set; } = new();
    public List<string> ProcessingLogs { get; set; } = new();
}

/// <summary>
/// 增强进度
/// </summary>
public class AugmentationProgress
{
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public int CurrentItem { get; set; }
    public string CurrentFileName { get; set; } = "";
    public int GeneratedSamples { get; set; }
    public double ProgressPercentage => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
    public string Status { get; set; } = "";
}
