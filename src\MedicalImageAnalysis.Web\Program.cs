using MedicalImageAnalysis.Application.Services;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Infrastructure.Services;
using MedicalImageAnalysis.Web.Hubs;
using Serilog;
using Blazorise;
using Blazorise.Bootstrap5;
using Blazorise.Icons.FontAwesome;

var builder = WebApplication.CreateBuilder(args);

// 配置 Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/medical-image-analysis-web-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务到容器
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// 添加 Blazorise 服务
builder.Services
    .AddBlazorise(options =>
    {
        options.Immediate = true;
    })
    .AddBootstrap5Providers()
    .AddFontAwesomeIcons();

// 添加 SignalR
builder.Services.AddSignalR();

// 注册应用服务
builder.Services.AddScoped<IDicomService, DicomService>();
builder.Services.AddScoped<IImageProcessingService, ImageProcessingService>();
builder.Services.AddScoped<IAnnotationService, AnnotationService>();
builder.Services.AddScoped<IDirectoryService, DirectoryService>();
builder.Services.AddScoped<StudyProcessingService>();

// 注册 YOLO 服务的占位符实现
builder.Services.AddScoped<IYoloService, YoloServicePlaceholder>();

// 配置文件上传限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 500 * 1024 * 1024; // 500MB
});

var app = builder.Build();

// 配置 HTTP 请求管道
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// 禁用 HTTPS 重定向以避免端口问题
// app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.MapRazorPages();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

// 添加 SignalR Hub
app.MapHub<MedicalImageAnalysis.Web.Hubs.ProcessingHub>("/processingHub");

try
{
    Log.Information("启动医学影像解析系统 Web 应用");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Web 应用启动失败");
}
finally
{
    Log.CloseAndFlush();
}



/// <summary>
/// YOLO 服务的占位符实现，用于演示
/// </summary>
public class YoloServicePlaceholder : IYoloService
{
    private readonly ILogger<YoloServicePlaceholder> _logger;

    public YoloServicePlaceholder(ILogger<YoloServicePlaceholder> logger)
    {
        _logger = logger;
    }

    public Task<TrainingResult> TrainModelAsync(YoloTrainingConfig trainingConfig, IProgress<TrainingProgress>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 训练功能尚未实现");
        return Task.FromResult(new TrainingResult
        {
            Success = false,
            ErrorMessage = "YOLO 训练功能尚未实现"
        });
    }

    public Task<ValidationResult> ValidateModelAsync(string modelPath, string validationDataPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 验证功能尚未实现");
        return Task.FromResult(new ValidationResult
        {
            Success = false,
            ErrorMessage = "YOLO 验证功能尚未实现"
        });
    }

    public Task<List<Detection>> InferAsync(string modelPath, byte[] imageData, YoloInferenceConfig inferenceConfig, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 推理功能尚未实现");

        // 返回模拟检测结果
        var detections = new List<Detection>
        {
            new Detection
            {
                Label = "示例检测",
                Confidence = 0.85,
                ClassId = 0,
                BoundingBox = new BoundingBox
                {
                    CenterX = 0.5,
                    CenterY = 0.5,
                    Width = 0.2,
                    Height = 0.2
                }
            }
        };

        return Task.FromResult(detections);
    }

    public Task<List<BatchDetectionResult>> BatchInferAsync(string modelPath, IEnumerable<string> imagePaths, YoloInferenceConfig inferenceConfig, IProgress<int>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 批量推理功能尚未实现");
        return Task.FromResult(new List<BatchDetectionResult>());
    }

    public Task<string> ExportModelAsync(string modelPath, ModelExportFormat exportFormat, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 模型导出功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<YoloModelInfo> GetModelInfoAsync(string modelPath)
    {
        _logger.LogWarning("YOLO 模型信息获取功能尚未实现");
        return Task.FromResult(new YoloModelInfo
        {
            Name = "示例模型",
            Version = "1.0.0",
            InputSize = (640, 640),
            ClassCount = 1,
            ClassNames = { "示例类别" }
        });
    }

    public Task<string> CreateDatasetConfigAsync(DatasetConfig datasetConfig, string outputPath)
    {
        _logger.LogWarning("YOLO 数据集配置创建功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<DatasetValidationResult> ValidateDatasetAsync(string datasetPath)
    {
        _logger.LogWarning("YOLO 数据集验证功能尚未实现");
        return Task.FromResult(new DatasetValidationResult
        {
            IsValid = false,
            Errors = { "YOLO 数据集验证功能尚未实现" }
        });
    }

    public Task<string> GenerateDataAugmentationAsync(string sourceDataPath, DataAugmentationConfig augmentationConfig, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 数据增强功能尚未实现");
        return Task.FromResult(string.Empty);
    }
}
