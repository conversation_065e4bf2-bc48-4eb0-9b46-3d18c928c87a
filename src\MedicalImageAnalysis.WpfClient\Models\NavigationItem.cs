namespace MedicalImageAnalysis.WpfClient.Models;

/// <summary>
/// 导航项模型
/// </summary>
public class NavigationItem
{
    public string Title { get; set; } = "";
    public string Icon { get; set; } = "";
    public Type? ViewType { get; set; }
    public bool IsEnabled { get; set; } = true;
    public string? Description { get; set; }
}

/// <summary>
/// 连接状态枚举
/// </summary>
public enum ConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Error
}

/// <summary>
/// 通知类型枚举
/// </summary>
public enum NotificationType
{
    Info,
    Success,
    Warning,
    Error
}

/// <summary>
/// 进度信息模型
/// </summary>
public class ProgressInfo
{
    public double Percentage { get; set; }
    public string Message { get; set; } = "";
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 系统状态模型
/// </summary>
public class SystemStatus
{
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ActiveTasks { get; set; }
    public int QueuedTasks { get; set; }
    public string Health { get; set; } = "Unknown";
    public DateTime LastUpdate { get; set; } = DateTime.Now;
}

/// <summary>
/// 应用设置模型
/// </summary>
public class AppSettings
{
    public string ApiBaseUrl { get; set; } = "http://localhost:5000";
    public string SignalRUrl { get; set; } = "http://localhost:5000/hubs/processing";
    public int ConnectionTimeout { get; set; } = 30;
    public bool AutoConnect { get; set; } = true;
    public bool EnableNotifications { get; set; } = true;
    public string Theme { get; set; } = "Light";
    public string Language { get; set; } = "zh-CN";
    public string DefaultOutputDirectory { get; set; } = "Output";
    public int MaxConcurrentTasks { get; set; } = 3;
    public bool SaveWindowState { get; set; } = true;
}

/// <summary>
/// 文件信息模型
/// </summary>
public class FileInfo
{
    public string Name { get; set; } = "";
    public string Path { get; set; } = "";
    public long Size { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime ModifiedTime { get; set; }
    public string Extension { get; set; } = "";
    public string Type { get; set; } = "";
}

/// <summary>
/// DICOM信息模型
/// </summary>
public class DicomInfo
{
    public string PatientName { get; set; } = "";
    public string PatientId { get; set; } = "";
    public string StudyDescription { get; set; } = "";
    public string SeriesDescription { get; set; } = "";
    public string Modality { get; set; } = "";
    public DateTime StudyDate { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string SopInstanceUid { get; set; } = "";
    public Dictionary<string, string> Tags { get; set; } = new();
}

/// <summary>
/// 训练配置模型
/// </summary>
public class TrainingConfig
{
    public string ExperimentName { get; set; } = "";
    public string DatasetPath { get; set; } = "";
    public string ModelType { get; set; } = "yolo11n";
    public int Epochs { get; set; } = 100;
    public int BatchSize { get; set; } = 16;
    public double LearningRate { get; set; } = 0.01;
    public int ImageSize { get; set; } = 640;
    public bool UsePretrainedWeights { get; set; } = true;
    public string OutputDirectory { get; set; } = "";
}

/// <summary>
/// 训练进度模型
/// </summary>
public class TrainingProgress
{
    public int CurrentEpoch { get; set; }
    public int TotalEpochs { get; set; }
    public double Loss { get; set; }
    public double Accuracy { get; set; }
    public double mAP { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public string Status { get; set; } = "";
}

/// <summary>
/// 推理结果模型
/// </summary>
public class InferenceResult
{
    public string ImagePath { get; set; } = "";
    public List<Detection> Detections { get; set; } = new();
    public double InferenceTime { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 检测结果模型
/// </summary>
public class Detection
{
    public string ClassName { get; set; } = "";
    public double Confidence { get; set; }
    public BoundingBox BoundingBox { get; set; } = new();
}

/// <summary>
/// 边界框模型
/// </summary>
public class BoundingBox
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Width { get; set; }
    public double Height { get; set; }
}
