<Window x:Class="MedicalImageAnalysis.Wpf.HelpWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="帮助文档"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        Background="White">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧导航 -->
        <Border Grid.Column="0"
                Background="LightGray"
                BorderBrush="Gray"
                BorderThickness="0,0,1,0">
            <StackPanel Margin="16">
                <TextBlock Text="帮助目录" 
                         FontSize="18" 
                         FontWeight="Medium"
                         Margin="0,0,0,16"/>
                
                <ListBox x:Name="HelpNavigationListBox"
                       SelectionChanged="HelpNavigationListBox_SelectionChanged"
                       Background="Transparent"
                       BorderThickness="0">
                    
                    <ListBoxItem x:Name="GettingStartedItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="▶"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="快速开始"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="DicomUploadItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="DICOM上传"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="ImageProcessingItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖼️"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="图像处理"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="AnnotationItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🏷️"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="智能标注"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="ModelTrainingItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🧠"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="模型训练"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="TroubleshootingItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❓"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="故障排除"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem x:Name="AboutItem">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="ℹ️"
                                     FontSize="16"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="关于"/>
                        </StackPanel>
                    </ListBoxItem>
                </ListBox>
            </StackPanel>
        </Border>

        <!-- 右侧内容 -->
        <ScrollViewer Grid.Column="1" 
                    VerticalScrollBarVisibility="Auto"
                    Padding="24">
            <StackPanel x:Name="HelpContentPanel">
                <!-- 默认显示快速开始内容 -->
                <TextBlock Text="快速开始" 
                         FontSize="24" 
                         FontWeight="Medium"
                         Margin="0,0,0,16"/>
                
                <TextBlock Text="欢迎使用医学影像解析系统！" 
                         FontSize="16"
                         Margin="0,0,0,12"/>
                
                <TextBlock TextWrapping="Wrap" Margin="0,0,0,16">
                    <Run Text="本系统是一个基于人工智能的医学影像分析平台，支持DICOM文件的上传、处理、标注和AI模型训练。"/>
                </TextBlock>
                
                <TextBlock Text="主要功能：" 
                         FontWeight="Medium"
                         FontSize="14"
                         Margin="0,0,0,8"/>
                
                <StackPanel Margin="16,0,0,16">
                    <TextBlock Text="• DICOM文件上传和验证" Margin="0,4"/>
                    <TextBlock Text="• 医学影像预处理和增强" Margin="0,4"/>
                    <TextBlock Text="• AI辅助智能标注" Margin="0,4"/>
                    <TextBlock Text="• 深度学习模型训练" Margin="0,4"/>
                    <TextBlock Text="• 统计分析和报告生成" Margin="0,4"/>
                    <TextBlock Text="• 文件和数据集管理" Margin="0,4"/>
                </StackPanel>
                
                <TextBlock Text="系统要求：" 
                         FontWeight="Medium"
                         FontSize="14"
                         Margin="0,0,0,8"/>
                
                <StackPanel Margin="16,0,0,16">
                    <TextBlock Text="• Windows 10/11 (64位)" Margin="0,4"/>
                    <TextBlock Text="• .NET 8.0 运行时" Margin="0,4"/>
                    <TextBlock Text="• 至少 8GB 内存" Margin="0,4"/>
                    <TextBlock Text="• 支持CUDA的显卡（推荐）" Margin="0,4"/>
                    <TextBlock Text="• 至少 10GB 可用磁盘空间" Margin="0,4"/>
                </StackPanel>
                
                <Border Background="LightYellow"
                        BorderBrush="Orange"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,16">
                    <StackPanel Margin="16">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <TextBlock Text="💡"
                                     FontSize="20"
                                     Foreground="Orange"
                                     Margin="0,0,8,0"/>
                            <TextBlock Text="提示" FontWeight="Medium"/>
                        </StackPanel>
                        <TextBlock TextWrapping="Wrap">
                            <Run Text="首次使用时，请先在设置中配置API服务器地址和数据存储目录。如果您是离线使用，系统会自动切换到离线模式。"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- 底部按钮 -->
                <StackPanel Orientation="Horizontal"
                          HorizontalAlignment="Right"
                          Margin="0,24,0,0">
                    <Button Content="关闭"
                          Background="DodgerBlue"
                          Foreground="White"
                          Padding="16,8"
                          BorderThickness="0"
                          Click="Close_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
