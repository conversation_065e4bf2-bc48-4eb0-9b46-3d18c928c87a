﻿#pragma checksum "..\..\..\..\Windows\HelpWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "76493D463B0E541FB11ACFEACCD35E001995794E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf {
    
    
    /// <summary>
    /// HelpWindow
    /// </summary>
    public partial class HelpWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox HelpNavigationListBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem GettingStartedItem;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem DicomUploadItem;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem ImageProcessingItem;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem AnnotationItem;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem ModelTrainingItem;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem TroubleshootingItem;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem AboutItem;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Windows\HelpWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel HelpContentPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/windows/helpwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\HelpWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HelpNavigationListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 28 "..\..\..\..\Windows\HelpWindow.xaml"
            this.HelpNavigationListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.HelpNavigationListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.GettingStartedItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 3:
            this.DicomUploadItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 4:
            this.ImageProcessingItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 5:
            this.AnnotationItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 6:
            this.ModelTrainingItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 7:
            this.TroubleshootingItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 8:
            this.AboutItem = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 9:
            this.HelpContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            
            #line 172 "..\..\..\..\Windows\HelpWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

