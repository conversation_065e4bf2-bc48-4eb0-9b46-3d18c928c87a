using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 简化的DICOM服务接口
/// </summary>
public interface ISimpleDicomService
{
    /// <summary>
    /// 解析DICOM文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>解析结果</returns>
    Task<Models.DicomParseResult> ParseDicomFileAsync(string filePath);

    /// <summary>
    /// 验证DICOM文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>验证结果</returns>
    Task<Models.DicomValidationResult> ValidateDicomFileAsync(string filePath);

    /// <summary>
    /// 提取像素数据
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>像素数据</returns>
    Task<Models.PixelData?> ExtractPixelDataAsync(string filePath);

    /// <summary>
    /// 获取DICOM标签值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="tag">标签</param>
    /// <returns>标签值</returns>
    Task<string?> GetDicomTagValueAsync(string filePath, uint tag);

    /// <summary>
    /// 转换为标准图像格式
    /// </summary>
    /// <param name="dicomPath">DICOM文件路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="format">图像格式</param>
    /// <returns>输出文件路径</returns>
    Task<string?> ConvertToImageAsync(string dicomPath, string outputPath, Models.ImageFormat format = Models.ImageFormat.Png);

    /// <summary>
    /// 批量处理DICOM文件
    /// </summary>
    /// <param name="filePaths">文件路径集合</param>
    /// <param name="progress">进度报告</param>
    /// <returns>批量处理结果</returns>
    Task<Models.BatchProcessResult> ProcessDicomBatchAsync(IEnumerable<string> filePaths, IProgress<string>? progress = null);

    /// <summary>
    /// 提取DICOM元数据
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>元数据</returns>
    Task<Models.DicomMetadata?> ExtractMetadataAsync(string filePath);

    /// <summary>
    /// 获取图像的坐标信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>坐标信息</returns>
    Task<Models.ImageCoordinateInfo?> GetImageCoordinateInfoAsync(string filePath);
}


{
    /// <summary>
    /// 图像位置（患者坐标系）
    /// </summary>
    public (double X, double Y, double Z) ImagePosition { get; set; }

    /// <summary>
    /// 图像方向余弦（患者坐标系）
    /// </summary>
    public double[] ImageOrientationPatient { get; set; } = new double[6];

    /// <summary>
    /// 像素间距 (行间距, 列间距)
    /// </summary>
    public (double Row, double Column) PixelSpacing { get; set; }

    /// <summary>
    /// 切片厚度
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 切片位置
    /// </summary>
    public double SliceLocation { get; set; }

    /// <summary>
    /// 图像行数
    /// </summary>
    public int Rows { get; set; }

    /// <summary>
    /// 图像列数
    /// </summary>
    public int Columns { get; set; }

    /// <summary>
    /// 将像素坐标转换为患者坐标
    /// </summary>
    /// <param name="pixelRow">像素行</param>
    /// <param name="pixelColumn">像素列</param>
    /// <returns>患者坐标</returns>
    public (double X, double Y, double Z) PixelToPatientCoordinate(int pixelRow, int pixelColumn)
    {
        // 计算像素在图像坐标系中的位置
        var deltaI = pixelColumn * PixelSpacing.Column;
        var deltaJ = pixelRow * PixelSpacing.Row;

        // 转换到患者坐标系
        var x = ImagePosition.X + deltaI * ImageOrientationPatient[0] + deltaJ * ImageOrientationPatient[3];
        var y = ImagePosition.Y + deltaI * ImageOrientationPatient[1] + deltaJ * ImageOrientationPatient[4];
        var z = ImagePosition.Z + deltaI * ImageOrientationPatient[2] + deltaJ * ImageOrientationPatient[5];

        return (x, y, z);
    }

    /// <summary>
    /// 将患者坐标转换为像素坐标
    /// </summary>
    /// <param name="patientX">患者X坐标</param>
    /// <param name="patientY">患者Y坐标</param>
    /// <param name="patientZ">患者Z坐标</param>
    /// <returns>像素坐标</returns>
    public (int Row, int Column) PatientToPixelCoordinate(double patientX, double patientY, double patientZ)
    {
        // 计算相对于图像原点的偏移
        var deltaX = patientX - ImagePosition.X;
        var deltaY = patientY - ImagePosition.Y;
        var deltaZ = patientZ - ImagePosition.Z;

        // 使用图像方向余弦的逆变换
        // 这是一个简化的实现，实际应该使用矩阵逆变换
        var deltaI = (deltaX * ImageOrientationPatient[0] + deltaY * ImageOrientationPatient[1] + deltaZ * ImageOrientationPatient[2]) / PixelSpacing.Column;
        var deltaJ = (deltaX * ImageOrientationPatient[3] + deltaY * ImageOrientationPatient[4] + deltaZ * ImageOrientationPatient[5]) / PixelSpacing.Row;

        var pixelColumn = (int)Math.Round(deltaI);
        var pixelRow = (int)Math.Round(deltaJ);

        return (pixelRow, pixelColumn);
    }

    /// <summary>
    /// 获取体素的物理尺寸
    /// </summary>
    /// <returns>体素尺寸 (宽度, 高度, 深度)</returns>
    public (double Width, double Height, double Depth) GetVoxelSize()
    {
        return (PixelSpacing.Column, PixelSpacing.Row, SliceThickness);
    }

    /// <summary>
    /// 计算两个像素点之间的物理距离
    /// </summary>
    /// <param name="row1">第一个点的行</param>
    /// <param name="col1">第一个点的列</param>
    /// <param name="row2">第二个点的行</param>
    /// <param name="col2">第二个点的列</param>
    /// <returns>物理距离（毫米）</returns>
    public double CalculatePixelDistance(int row1, int col1, int row2, int col2)
    {
        var coord1 = PixelToPatientCoordinate(row1, col1);
        var coord2 = PixelToPatientCoordinate(row2, col2);

        var dx = coord2.X - coord1.X;
        var dy = coord2.Y - coord1.Y;
        var dz = coord2.Z - coord1.Z;

        return Math.Sqrt(dx * dx + dy * dy + dz * dz);
    }

    /// <summary>
    /// 计算像素区域的物理面积
    /// </summary>
    /// <param name="pixelCount">像素数量</param>
    /// <returns>物理面积（平方毫米）</returns>
    public double CalculatePixelArea(int pixelCount)
    {
        return pixelCount * PixelSpacing.Row * PixelSpacing.Column;
    }

    /// <summary>
    /// 计算体素的物理体积
    /// </summary>
    /// <param name="voxelCount">体素数量</param>
    /// <returns>物理体积（立方毫米）</returns>
    public double CalculateVoxelVolume(int voxelCount)
    {
        return voxelCount * PixelSpacing.Row * PixelSpacing.Column * SliceThickness;
    }
}
