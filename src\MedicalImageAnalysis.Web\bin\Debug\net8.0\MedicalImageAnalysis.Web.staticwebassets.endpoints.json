{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css", "AssetFile": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "87508"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs="}]}, {"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.min.css", "AssetFile": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70296"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao="}]}, {"Route": "_content/Blazorise.Bootstrap5/modal.js", "AssetFile": "_content/Blazorise.Bootstrap5/modal.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3939"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI="}]}, {"Route": "_content/Blazorise.Bootstrap5/tooltip.js", "AssetFile": "_content/Blazorise.Bootstrap5/tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "421"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI="}]}, {"Route": "_content/Blazorise/blazorise.css", "AssetFile": "_content/Blazorise/blazorise.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67982"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw="}]}, {"Route": "_content/Blazorise/blazorise.min.css", "AssetFile": "_content/Blazorise/blazorise.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60000"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM="}]}, {"Route": "_content/Blazorise/breakpoint.js", "AssetFile": "_content/Blazorise/breakpoint.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2379"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4="}]}, {"Route": "_content/Blazorise/button.js", "AssetFile": "_content/Blazorise/button.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0="}]}, {"Route": "_content/Blazorise/closable.js", "AssetFile": "_content/Blazorise/closable.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No="}]}, {"Route": "_content/Blazorise/colorPicker.js", "AssetFile": "_content/Blazorise/colorPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7047"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0="}]}, {"Route": "_content/Blazorise/datePicker.js", "AssetFile": "_content/Blazorise/datePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12869"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0="}]}, {"Route": "_content/Blazorise/dragDrop.js", "AssetFile": "_content/Blazorise/dragDrop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2440"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q="}]}, {"Route": "_content/Blazorise/dropdown.js", "AssetFile": "_content/Blazorise/dropdown.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1372"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo="}]}, {"Route": "_content/Blazorise/fileEdit.js", "AssetFile": "_content/Blazorise/fileEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5702"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE="}]}, {"Route": "_content/Blazorise/filePicker.js", "AssetFile": "_content/Blazorise/filePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU="}]}, {"Route": "_content/Blazorise/floatingUi.js", "AssetFile": "_content/Blazorise/floatingUi.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg="}]}, {"Route": "_content/Blazorise/inputMask.js", "AssetFile": "_content/Blazorise/inputMask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2604"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os="}]}, {"Route": "_content/Blazorise/io.js", "AssetFile": "_content/Blazorise/io.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY="}]}, {"Route": "_content/Blazorise/memoEdit.js", "AssetFile": "_content/Blazorise/memoEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y="}]}, {"Route": "_content/Blazorise/numericPicker.js", "AssetFile": "_content/Blazorise/numericPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY="}]}, {"Route": "_content/Blazorise/observer.js", "AssetFile": "_content/Blazorise/observer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3682"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130="}]}, {"Route": "_content/Blazorise/table.js", "AssetFile": "_content/Blazorise/table.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7739"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE="}]}, {"Route": "_content/Blazorise/textEdit.js", "AssetFile": "_content/Blazorise/textEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1722"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A="}]}, {"Route": "_content/Blazorise/theme.js", "AssetFile": "_content/Blazorise/theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1487"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA="}]}, {"Route": "_content/Blazorise/timePicker.js", "AssetFile": "_content/Blazorise/timePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg="}]}, {"Route": "_content/Blazorise/tooltip.js", "AssetFile": "_content/Blazorise/tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54020"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8="}]}, {"Route": "_content/Blazorise/utilities.js", "AssetFile": "_content/Blazorise/utilities.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8="}]}, {"Route": "_content/Blazorise/validators/DateTimeMaskValidator.js", "AssetFile": "_content/Blazorise/validators/DateTimeMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk="}]}, {"Route": "_content/Blazorise/validators/NoValidator.js", "AssetFile": "_content/Blazorise/validators/NoValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw="}]}, {"Route": "_content/Blazorise/validators/NumericMaskValidator.js", "AssetFile": "_content/Blazorise/validators/NumericMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6408"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE="}]}, {"Route": "_content/Blazorise/validators/RegExMaskValidator.js", "AssetFile": "_content/Blazorise/validators/RegExMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM="}]}, {"Route": "_content/Blazorise/vendors/Behave.js", "AssetFile": "_content/Blazorise/vendors/Behave.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE="}]}, {"Route": "_content/Blazorise/vendors/Pickr.js", "AssetFile": "_content/Blazorise/vendors/Pickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM="}]}, {"Route": "_content/Blazorise/vendors/autoNumeric.js", "AssetFile": "_content/Blazorise/vendors/autoNumeric.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo="}]}, {"Route": "_content/Blazorise/vendors/flatpickr.js", "AssetFile": "_content/Blazorise/vendors/flatpickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "62541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88="}]}, {"Route": "_content/Blazorise/vendors/floating-ui-core.js", "AssetFile": "_content/Blazorise/vendors/floating-ui-core.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15103"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0="}]}, {"Route": "_content/Blazorise/vendors/floating-ui.js", "AssetFile": "_content/Blazorise/vendors/floating-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10628"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI="}]}, {"Route": "_content/Blazorise/vendors/inputmask.js", "AssetFile": "_content/Blazorise/vendors/inputmask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "140250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:33:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs="}]}, {"Route": "_content/Blazorise/vendors/jsencrypt.js", "AssetFile": "_content/Blazorise/vendors/jsencrypt.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55434"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM="}]}, {"Route": "_content/Blazorise/vendors/sha512.js", "AssetFile": "_content/Blazorise/vendors/sha512.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17899"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU="}]}, {"Route": "css/app.0009rc8gzr.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6817"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:38:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0009rc8gzr"}, {"Name": "integrity", "Value": "sha256-wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6817"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:38:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5289"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 23:43:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU="}]}, {"Route": "css/bootstrap/bootstrap.min.g69rteb3dh.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5289"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 23:43:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g69rteb3dh"}, {"Name": "integrity", "Value": "sha256-OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "js/app.d3kcq3z4hl.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9208"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:34:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3kcq3z4hl"}, {"Name": "integrity", "Value": "sha256-R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9208"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:34:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s="}]}, {"Route": "js/directory-manager.js", "AssetFile": "js/directory-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:33:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc="}]}, {"Route": "js/directory-manager.lbp7dec8vk.js", "AssetFile": "js/directory-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:33:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbp7dec8vk"}, {"Name": "integrity", "Value": "sha256-D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc="}, {"Name": "label", "Value": "js/directory-manager.js"}]}]}