namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 患者信息实体
/// </summary>
public class Patient
{
    public Guid Id { get; set; }
    public string PatientId { get; set; } = string.Empty;
    public string PatientName { get; set; } = string.Empty;
    public DateTime? PatientBirthDate { get; set; }
    public string PatientSex { get; set; } = string.Empty;
    public string PatientAge { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // 导航属性
    public virtual ICollection<DicomStudy> Studies { get; set; } = new List<DicomStudy>();
}
