using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 实时通知服务
/// </summary>
public class RealTimeNotificationService : IRealTimeNotificationService
{
    private readonly IHubContext<ProcessingHub> _hubContext;
    private readonly ILogger<RealTimeNotificationService> _logger;

    public RealTimeNotificationService(
        IHubContext<ProcessingHub> hubContext,
        ILogger<RealTimeNotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// 发送训练进度更新
    /// </summary>
    public async Task SendTrainingProgressAsync(string trainingId, TrainingProgressInfo progress)
    {
        try
        {
            await _hubContext.Clients.Group($"Training_{trainingId}")
                .SendAsync("TrainingProgress", progress);
            
            _logger.LogDebug("发送训练进度更新: {TrainingId}, Epoch: {Epoch}/{TotalEpochs}", 
                trainingId, progress.CurrentEpoch, progress.TotalEpochs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送训练进度更新失败: {TrainingId}", trainingId);
        }
    }

    /// <summary>
    /// 发送训练完成通知
    /// </summary>
    public async Task SendTrainingCompletedAsync(string trainingId, TrainingResult result)
    {
        try
        {
            await _hubContext.Clients.Group($"Training_{trainingId}")
                .SendAsync("TrainingCompleted", result);
            
            await _hubContext.Clients.All
                .SendAsync("Notification", new
                {
                    type = "success",
                    title = "训练完成",
                    message = $"训练任务 {trainingId} 已成功完成",
                    timestamp = DateTime.UtcNow
                });
            
            _logger.LogInformation("训练完成通知已发送: {TrainingId}", trainingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送训练完成通知失败: {TrainingId}", trainingId);
        }
    }

    /// <summary>
    /// 发送训练失败通知
    /// </summary>
    public async Task SendTrainingFailedAsync(string trainingId, string error)
    {
        try
        {
            await _hubContext.Clients.Group($"Training_{trainingId}")
                .SendAsync("TrainingFailed", error);
            
            await _hubContext.Clients.All
                .SendAsync("Notification", new
                {
                    type = "error",
                    title = "训练失败",
                    message = $"训练任务 {trainingId} 失败: {error}",
                    timestamp = DateTime.UtcNow
                });
            
            _logger.LogWarning("训练失败通知已发送: {TrainingId}, Error: {Error}", trainingId, error);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送训练失败通知失败: {TrainingId}", trainingId);
        }
    }

    /// <summary>
    /// 发送处理进度更新
    /// </summary>
    public async Task SendProcessingProgressAsync(string taskId, ProcessingProgressInfo progress)
    {
        try
        {
            await _hubContext.Clients.Group($"Processing_{taskId}")
                .SendAsync("ProcessingProgress", progress);
            
            _logger.LogDebug("发送处理进度更新: {TaskId}, Progress: {Progress}%", 
                taskId, progress.ProgressPercentage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送处理进度更新失败: {TaskId}", taskId);
        }
    }

    /// <summary>
    /// 发送处理完成通知
    /// </summary>
    public async Task SendProcessingCompletedAsync(string taskId, ProcessingResult result)
    {
        try
        {
            await _hubContext.Clients.Group($"Processing_{taskId}")
                .SendAsync("ProcessingCompleted", result);
            
            await _hubContext.Clients.All
                .SendAsync("Notification", new
                {
                    type = "success",
                    title = "处理完成",
                    message = $"处理任务 {taskId} 已完成",
                    timestamp = DateTime.UtcNow
                });
            
            _logger.LogInformation("处理完成通知已发送: {TaskId}", taskId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送处理完成通知失败: {TaskId}", taskId);
        }
    }

    /// <summary>
    /// 发送系统状态更新
    /// </summary>
    public async Task SendSystemStatusAsync(SystemStatusInfo status)
    {
        try
        {
            await _hubContext.Clients.All
                .SendAsync("SystemStatus", status);
            
            _logger.LogDebug("系统状态更新已发送: CPU: {CpuUsage}%, Memory: {MemoryUsage}%", 
                status.CpuUsage, status.MemoryUsage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送系统状态更新失败");
        }
    }

    /// <summary>
    /// 发送通用通知
    /// </summary>
    public async Task SendNotificationAsync(NotificationInfo notification)
    {
        try
        {
            await _hubContext.Clients.All
                .SendAsync("Notification", notification);
            
            _logger.LogInformation("通知已发送: {Type} - {Message}", notification.Type, notification.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送通知失败: {Message}", notification.Message);
        }
    }

    /// <summary>
    /// 发送给特定用户的通知
    /// </summary>
    public async Task SendNotificationToUserAsync(string userId, NotificationInfo notification)
    {
        try
        {
            await _hubContext.Clients.User(userId)
                .SendAsync("Notification", notification);
            
            _logger.LogInformation("用户通知已发送: {UserId} - {Type} - {Message}", 
                userId, notification.Type, notification.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送用户通知失败: {UserId} - {Message}", userId, notification.Message);
        }
    }

    /// <summary>
    /// 发送给特定组的通知
    /// </summary>
    public async Task SendNotificationToGroupAsync(string groupName, NotificationInfo notification)
    {
        try
        {
            await _hubContext.Clients.Group(groupName)
                .SendAsync("Notification", notification);
            
            _logger.LogInformation("组通知已发送: {GroupName} - {Type} - {Message}", 
                groupName, notification.Type, notification.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送组通知失败: {GroupName} - {Message}", groupName, notification.Message);
        }
    }

    /// <summary>
    /// 发送DICOM上传进度
    /// </summary>
    public async Task SendDicomUploadProgressAsync(string uploadId, DicomUploadProgressInfo progress)
    {
        try
        {
            await _hubContext.Clients.Group($"Upload_{uploadId}")
                .SendAsync("DicomUploadProgress", progress);
            
            _logger.LogDebug("DICOM上传进度已发送: {UploadId}, Files: {ProcessedFiles}/{TotalFiles}", 
                uploadId, progress.ProcessedFiles, progress.TotalFiles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送DICOM上传进度失败: {UploadId}", uploadId);
        }
    }

    /// <summary>
    /// 发送标注进度更新
    /// </summary>
    public async Task SendAnnotationProgressAsync(string projectId, AnnotationProgressInfo progress)
    {
        try
        {
            await _hubContext.Clients.Group($"Annotation_{projectId}")
                .SendAsync("AnnotationProgress", progress);
            
            _logger.LogDebug("标注进度已发送: {ProjectId}, Completed: {CompletedCount}/{TotalCount}", 
                projectId, progress.CompletedCount, progress.TotalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送标注进度失败: {ProjectId}", projectId);
        }
    }

    /// <summary>
    /// 发送模型推理结果
    /// </summary>
    public async Task SendInferenceResultAsync(string sessionId, InferenceResult result)
    {
        try
        {
            await _hubContext.Clients.Group($"Inference_{sessionId}")
                .SendAsync("InferenceResult", result);
            
            _logger.LogInformation("推理结果已发送: {SessionId}, Detections: {DetectionCount}", 
                sessionId, result.Detections?.Count ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送推理结果失败: {SessionId}", sessionId);
        }
    }
}

/// <summary>
/// ProcessingHub - SignalR Hub实现
/// </summary>
public class ProcessingHub : Hub
{
    private readonly ILogger<ProcessingHub> _logger;

    public ProcessingHub(ILogger<ProcessingHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("客户端连接: {ConnectionId}", Context.ConnectionId);
        await Groups.AddToGroupAsync(Context.ConnectionId, "AllClients");
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("客户端断开连接: {ConnectionId}, 异常: {Exception}", 
            Context.ConnectionId, exception?.Message);
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 加入训练组
    /// </summary>
    public async Task JoinTrainingGroup(string trainingId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Training_{trainingId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入训练组 {TrainingId}", 
            Context.ConnectionId, trainingId);
    }

    /// <summary>
    /// 离开训练组
    /// </summary>
    public async Task LeaveTrainingGroup(string trainingId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Training_{trainingId}");
        _logger.LogInformation("客户端 {ConnectionId} 离开训练组 {TrainingId}", 
            Context.ConnectionId, trainingId);
    }

    /// <summary>
    /// 加入处理组
    /// </summary>
    public async Task JoinProcessingGroup(string taskId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Processing_{taskId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入处理组 {TaskId}", 
            Context.ConnectionId, taskId);
    }

    /// <summary>
    /// 离开处理组
    /// </summary>
    public async Task LeaveProcessingGroup(string taskId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Processing_{taskId}");
        _logger.LogInformation("客户端 {ConnectionId} 离开处理组 {TaskId}", 
            Context.ConnectionId, taskId);
    }

    /// <summary>
    /// 加入上传组
    /// </summary>
    public async Task JoinUploadGroup(string uploadId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Upload_{uploadId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入上传组 {UploadId}", 
            Context.ConnectionId, uploadId);
    }

    /// <summary>
    /// 加入标注组
    /// </summary>
    public async Task JoinAnnotationGroup(string projectId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Annotation_{projectId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入标注组 {ProjectId}", 
            Context.ConnectionId, projectId);
    }

    /// <summary>
    /// 加入推理组
    /// </summary>
    public async Task JoinInferenceGroup(string sessionId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Inference_{sessionId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入推理组 {SessionId}", 
            Context.ConnectionId, sessionId);
    }

    /// <summary>
    /// 发送心跳
    /// </summary>
    public async Task SendHeartbeat()
    {
        await Clients.Caller.SendAsync("Heartbeat", DateTime.UtcNow);
    }
}
