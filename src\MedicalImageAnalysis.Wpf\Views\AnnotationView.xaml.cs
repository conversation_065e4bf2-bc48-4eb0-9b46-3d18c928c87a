using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

// WPF 类型别名已在 GlobalUsings.cs 中定义

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// AnnotationView.xaml 的交互逻辑
    /// </summary>
    public partial class AnnotationView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<AnnotationView> _logger;
        private BitmapSource? _currentImage;
        private bool _hasImage = false;
        private string _currentAnnotationTool = "";
        private bool _isDrawing = false;
        private System.Windows.Point _startPoint;
        private Shape? _currentShape;
        private readonly ObservableCollection<AnnotationItem> _annotations;

        public AnnotationView()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<AnnotationView>.Instance;
            _annotations = new ObservableCollection<AnnotationItem>();
            AnnotationListView.ItemsSource = _annotations;

            // 默认选择矩形工具
            RectangleToolButton.IsChecked = true;
            _currentAnnotationTool = "Rectangle";

            // 设置默认类别
            AnnotationCategoryComboBox.SelectedIndex = 0;
        }

        /// <summary>
        /// 打开图像文件
        /// </summary>
        private void OpenImage_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择图像文件",
                Filter = "图像文件 (*.png;*.jpg;*.jpeg;*.bmp;*.dcm)|*.png;*.jpg;*.jpeg;*.bmp;*.dcm|" +
                        "PNG 文件 (*.png)|*.png|" +
                        "JPEG 文件 (*.jpg;*.jpeg)|*.jpg;*.jpeg|" +
                        "BMP 文件 (*.bmp)|*.bmp|" +
                        "DICOM 文件 (*.dcm)|*.dcm|" +
                        "所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    LoadImage(openFileDialog.FileName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载图像失败: {FileName}", openFileDialog.FileName);
                    MessageBox.Show($"加载图像失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 加载图像文件
        /// </summary>
        private void LoadImage(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);

                if (fileInfo.Extension.ToLower() == ".dcm")
                {
                    // DICOM文件处理（简化版本）
                    MessageBox.Show("DICOM文件处理功能正在开发中，请使用标准图像格式。", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 加载标准图像格式
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                bitmap.Freeze();

                _currentImage = bitmap;

                AnnotationImage.Source = _currentImage;
                AnnotationImage.Visibility = Visibility.Visible;
                AnnotationCanvas.Visibility = Visibility.Visible;
                AnnotationPlaceholderPanel.Visibility = Visibility.Collapsed;
                AnnotationStatusPanel.Visibility = Visibility.Visible;

                // 设置画布大小与图像匹配
                AnnotationCanvas.Width = bitmap.PixelWidth;
                AnnotationCanvas.Height = bitmap.PixelHeight;

                _hasImage = true;
                UpdateStatus("图像加载完成，可以开始标注");

                _logger.LogInformation("图像加载成功: {FileName}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载图像时发生错误: {FileName}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 标注工具选择
        /// </summary>
        private void AnnotationTool_Click(object sender, RoutedEventArgs e)
        {
            if (sender is ToggleButton button)
            {
                // 取消其他工具的选择
                RectangleToolButton.IsChecked = false;
                CircleToolButton.IsChecked = false;
                PolygonToolButton.IsChecked = false;
                PointToolButton.IsChecked = false;

                // 选择当前工具
                button.IsChecked = true;
                _currentAnnotationTool = button.Tag?.ToString() ?? "";

                UpdateStatus($"已选择 {GetToolDisplayName(_currentAnnotationTool)} 工具");
            }
        }

        /// <summary>
        /// 获取工具显示名称
        /// </summary>
        private string GetToolDisplayName(string toolName)
        {
            return toolName switch
            {
                "Rectangle" => "矩形",
                "Circle" => "圆形",
                "Polygon" => "多边形",
                "Point" => "点",
                _ => "未知"
            };
        }

        /// <summary>
        /// 画布鼠标按下事件
        /// </summary>
        private void AnnotationCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!_hasImage || string.IsNullOrEmpty(_currentAnnotationTool)) return;

            _startPoint = e.GetPosition(AnnotationCanvas);
            _isDrawing = true;

            switch (_currentAnnotationTool)
            {
                case "Rectangle":
                    StartDrawingRectangle(_startPoint);
                    break;
                case "Circle":
                    StartDrawingCircle(_startPoint);
                    break;
                case "Point":
                    CreatePoint(_startPoint);
                    break;
            }

            AnnotationCanvas.CaptureMouse();
        }

        /// <summary>
        /// 画布鼠标移动事件
        /// </summary>
        private void AnnotationCanvas_MouseMove(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (!_isDrawing || _currentShape == null) return;

            var currentPoint = e.GetPosition(AnnotationCanvas);

            switch (_currentAnnotationTool)
            {
                case "Rectangle":
                    UpdateRectangle(currentPoint);
                    break;
                case "Circle":
                    UpdateCircle(currentPoint);
                    break;
            }
        }

        /// <summary>
        /// 画布鼠标释放事件
        /// </summary>
        private void AnnotationCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!_isDrawing) return;

            _isDrawing = false;
            AnnotationCanvas.ReleaseMouseCapture();

            if (_currentShape != null)
            {
                // 完成标注
                CompleteAnnotation();
            }
        }

        /// <summary>
        /// 开始绘制矩形
        /// </summary>
        private void StartDrawingRectangle(System.Windows.Point startPoint)
        {
            var rectangle = new Rectangle
            {
                Stroke = Brushes.Red,
                StrokeThickness = 2,
                Fill = Brushes.Transparent
            };

            Canvas.SetLeft(rectangle, startPoint.X);
            Canvas.SetTop(rectangle, startPoint.Y);

            AnnotationCanvas.Children.Add(rectangle);
            _currentShape = rectangle;
        }

        /// <summary>
        /// 更新矩形
        /// </summary>
        private void UpdateRectangle(System.Windows.Point currentPoint)
        {
            if (_currentShape is Rectangle rectangle)
            {
                var width = Math.Abs(currentPoint.X - _startPoint.X);
                var height = Math.Abs(currentPoint.Y - _startPoint.Y);

                rectangle.Width = width;
                rectangle.Height = height;

                Canvas.SetLeft(rectangle, Math.Min(_startPoint.X, currentPoint.X));
                Canvas.SetTop(rectangle, Math.Min(_startPoint.Y, currentPoint.Y));
            }
        }

        /// <summary>
        /// 开始绘制圆形
        /// </summary>
        private void StartDrawingCircle(System.Windows.Point startPoint)
        {
            var ellipse = new Ellipse
            {
                Stroke = Brushes.Blue,
                StrokeThickness = 2,
                Fill = Brushes.Transparent
            };

            Canvas.SetLeft(ellipse, startPoint.X);
            Canvas.SetTop(ellipse, startPoint.Y);

            AnnotationCanvas.Children.Add(ellipse);
            _currentShape = ellipse;
        }

        /// <summary>
        /// 更新圆形
        /// </summary>
        private void UpdateCircle(System.Windows.Point currentPoint)
        {
            if (_currentShape is Ellipse ellipse)
            {
                var radius = Math.Sqrt(Math.Pow(currentPoint.X - _startPoint.X, 2) +
                                     Math.Pow(currentPoint.Y - _startPoint.Y, 2));

                ellipse.Width = radius * 2;
                ellipse.Height = radius * 2;

                Canvas.SetLeft(ellipse, _startPoint.X - radius);
                Canvas.SetTop(ellipse, _startPoint.Y - radius);
            }
        }

        /// <summary>
        /// 创建点标注
        /// </summary>
        private void CreatePoint(System.Windows.Point point)
        {
            var ellipse = new Ellipse
            {
                Width = 8,
                Height = 8,
                Fill = Brushes.Green,
                Stroke = Brushes.DarkGreen,
                StrokeThickness = 1
            };

            Canvas.SetLeft(ellipse, point.X - 4);
            Canvas.SetTop(ellipse, point.Y - 4);

            AnnotationCanvas.Children.Add(ellipse);
            _currentShape = ellipse;

            // 点标注立即完成
            CompleteAnnotation();
        }

        /// <summary>
        /// 完成标注
        /// </summary>
        private void CompleteAnnotation()
        {
            if (_currentShape == null) return;

            var category = AnnotationCategoryComboBox.Text;
            if (string.IsNullOrEmpty(category))
            {
                category = "未分类";
            }

            var annotation = new AnnotationItem
            {
                Id = Guid.NewGuid(),
                Type = GetToolDisplayName(_currentAnnotationTool),
                Category = category,
                Confidence = 1.0, // 手动标注置信度为1.0
                Shape = _currentShape,
                CreatedTime = DateTime.Now
            };

            _annotations.Add(annotation);
            UpdateAnnotationCount();
            UpdateStatus($"已添加 {annotation.Type} 标注");

            _currentShape = null;
        }

        /// <summary>
        /// AI检测
        /// </summary>
        private async void AIDetection_Click(object sender, RoutedEventArgs e)
        {
            if (!_hasImage)
            {
                MessageBox.Show("请先加载图像。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                UpdateStatus("正在进行AI检测...");

                // 模拟AI检测过程
                await Task.Delay(2000);

                // 添加模拟的AI检测结果
                var aiAnnotation = new AnnotationItem
                {
                    Id = Guid.NewGuid(),
                    Type = "矩形",
                    Category = "AI检测-病灶",
                    Confidence = 0.85,
                    Shape = CreateSimulatedDetection(),
                    CreatedTime = DateTime.Now
                };

                _annotations.Add(aiAnnotation);
                UpdateAnnotationCount();
                UpdateStatus("AI检测完成");

                MessageBox.Show("AI检测完成！发现1个可疑区域。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI检测时发生错误");
                MessageBox.Show($"AI检测失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("AI检测失败");
            }
        }

        /// <summary>
        /// 创建模拟的AI检测结果
        /// </summary>
        private Shape CreateSimulatedDetection()
        {
            var rectangle = new Rectangle
            {
                Width = 100,
                Height = 80,
                Stroke = Brushes.Orange,
                StrokeThickness = 2,
                Fill = Brushes.Transparent,
                StrokeDashArray = new DoubleCollection { 5, 5 }
            };

            // 随机位置（确保在画布范围内）
            var random = new Random();
            var x = random.Next(50, (int)AnnotationCanvas.Width - 150);
            var y = random.Next(50, (int)AnnotationCanvas.Height - 130);

            Canvas.SetLeft(rectangle, x);
            Canvas.SetTop(rectangle, y);

            AnnotationCanvas.Children.Add(rectangle);
            return rectangle;
        }

        /// <summary>
        /// 清除所有标注
        /// </summary>
        private void ClearAnnotations_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要清除所有标注吗？", "确认",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                AnnotationCanvas.Children.Clear();
                _annotations.Clear();
                UpdateAnnotationCount();
                UpdateStatus("已清除所有标注");
            }
        }

        /// <summary>
        /// 添加自定义类别
        /// </summary>
        private void AddCategory_Click(object sender, RoutedEventArgs e)
        {
            var customCategory = CustomCategoryTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(customCategory))
            {
                var newItem = new ComboBoxItem { Content = customCategory };
                AnnotationCategoryComboBox.Items.Add(newItem);
                AnnotationCategoryComboBox.SelectedItem = newItem;
                CustomCategoryTextBox.Clear();

                MessageBox.Show($"已添加类别：{customCategory}", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 标注列表选择变化
        /// </summary>
        private void AnnotationListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                // 高亮显示选中的标注
                HighlightAnnotation(annotation);
            }
        }

        /// <summary>
        /// 高亮显示标注
        /// </summary>
        private void HighlightAnnotation(AnnotationItem annotation)
        {
            // 重置所有标注的样式
            foreach (var child in AnnotationCanvas.Children.OfType<Shape>())
            {
                if (child.StrokeThickness == 4) // 之前被高亮的
                {
                    child.StrokeThickness = 2;
                }
            }

            // 高亮当前选中的标注
            if (annotation.Shape != null)
            {
                annotation.Shape.StrokeThickness = 4;
            }
        }

        /// <summary>
        /// 编辑标注
        /// </summary>
        private void EditAnnotation_Click(object sender, RoutedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                // 这里可以打开编辑对话框
                MessageBox.Show($"编辑标注功能正在开发中。\n当前选中：{annotation.Type} - {annotation.Category}",
                              "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("请先选择要编辑的标注。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 删除标注
        /// </summary>
        private void DeleteAnnotation_Click(object sender, RoutedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                var result = MessageBox.Show($"确定要删除标注\"{annotation.Category}\"吗？", "确认",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    // 从画布移除形状
                    if (annotation.Shape != null)
                    {
                        AnnotationCanvas.Children.Remove(annotation.Shape);
                    }

                    // 从列表移除
                    _annotations.Remove(annotation);
                    UpdateAnnotationCount();
                    UpdateStatus("已删除标注");
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的标注。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 导出标注数据
        /// </summary>
        private void ExportAnnotations_Click(object sender, RoutedEventArgs e)
        {
            if (!_annotations.Any())
            {
                MessageBox.Show("没有可导出的标注数据。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出标注数据",
                Filter = "JSON 文件 (*.json)|*.json|" +
                        "文本文件 (*.txt)|*.txt|" +
                        "所有文件 (*.*)|*.*",
                DefaultExt = "json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportAnnotationsToFile(saveFileDialog.FileName);
                    MessageBox.Show("标注数据导出成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "导出标注数据失败");
                    MessageBox.Show($"导出失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 导出标注数据到文件
        /// </summary>
        private void ExportAnnotationsToFile(string filePath)
        {
            var exportData = _annotations.Select(a => new
            {
                Id = a.Id,
                Type = a.Type,
                Category = a.Category,
                Confidence = a.Confidence,
                CreatedTime = a.CreatedTime,
                // 简化的位置信息
                Position = GetShapePosition(a.Shape)
            }).ToList();

            var json = System.Text.Json.JsonSerializer.Serialize(exportData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// 获取形状位置信息
        /// </summary>
        private object GetShapePosition(Shape? shape)
        {
            if (shape == null) return new { };

            var left = Canvas.GetLeft(shape);
            var top = Canvas.GetTop(shape);

            return shape switch
            {
                Rectangle rect => new { Left = left, Top = top, Width = rect.Width, Height = rect.Height },
                Ellipse ellipse => new { Left = left, Top = top, Width = ellipse.Width, Height = ellipse.Height },
                _ => new { Left = left, Top = top }
            };
        }

        /// <summary>
        /// 保存标注
        /// </summary>
        private void SaveAnnotations_Click(object sender, RoutedEventArgs e)
        {
            if (!_annotations.Any())
            {
                MessageBox.Show("没有可保存的标注数据。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Title = "保存标注文件",
                Filter = "标注文件 (*.ann)|*.ann|JSON 文件 (*.json)|*.json",
                DefaultExt = "ann"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportAnnotationsToFile(saveFileDialog.FileName);
                    MessageBox.Show("标注文件保存成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存标注文件失败");
                    MessageBox.Show($"保存失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 加载标注
        /// </summary>
        private void LoadAnnotations_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "加载标注文件",
                Filter = "标注文件 (*.ann)|*.ann|JSON 文件 (*.json)|*.json|所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    LoadAnnotationsFromFile(openFileDialog.FileName);
                    MessageBox.Show("标注文件加载成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载标注文件失败");
                    MessageBox.Show($"加载失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 从文件加载标注数据
        /// </summary>
        private void LoadAnnotationsFromFile(string filePath)
        {
            // 简化的加载实现
            MessageBox.Show("标注文件加载功能正在开发中。", "提示",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        private void UpdateStatus(string status)
        {
            AnnotationStatusText.Text = status;
        }

        /// <summary>
        /// 更新标注数量
        /// </summary>
        private void UpdateAnnotationCount()
        {
            AnnotationCountText.Text = $"标注数量: {_annotations.Count}";
        }
    }

    /// <summary>
    /// 标注项目模型
    /// </summary>
    public class AnnotationItem
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = "";
        public string Category { get; set; } = "";
        public double Confidence { get; set; }
        public Shape? Shape { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
