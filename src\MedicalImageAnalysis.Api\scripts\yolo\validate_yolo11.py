#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11 模型验证脚本
评估模型在验证集上的性能
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any
import yaml

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from ultralytics import YOLO
    from ultralytics.utils import LOGGER
    LOGGER.setLevel(logging.WARNING)
except ImportError:
    logger.error("请安装ultralytics库: pip install ultralytics>=8.3.0")
    sys.exit(1)

class YOLOv11Validator:
    """YOLOv11 验证器"""
    
    def __init__(self, model_path: str, dataset_path: str):
        """初始化验证器
        
        Args:
            model_path: 模型文件路径
            dataset_path: 数据集路径或配置文件路径
        """
        self.model_path = model_path
        self.dataset_path = dataset_path
        
        # 加载模型
        try:
            self.model = YOLO(model_path)
            logger.info(f"加载模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def validate(self, output_dir: str = None, **kwargs) -> Dict[str, Any]:
        """执行模型验证
        
        Args:
            output_dir: 输出目录
            **kwargs: 其他验证参数
            
        Returns:
            验证结果
        """
        try:
            logger.info("开始模型验证")
            
            # 验证参数
            val_args = {
                'data': self.dataset_path,
                'imgsz': kwargs.get('image_size', 640),
                'batch': kwargs.get('batch_size', 16),
                'conf': kwargs.get('confidence_threshold', 0.001),
                'iou': kwargs.get('iou_threshold', 0.6),
                'max_det': kwargs.get('max_detections', 300),
                'half': kwargs.get('half_precision', False),
                'device': kwargs.get('device', 'auto'),
                'dnn': kwargs.get('dnn', False),
                'plots': kwargs.get('plots', True),
                'save_json': True,
                'save_hybrid': kwargs.get('save_hybrid', False),
                'augment': kwargs.get('augment', False),
                'verbose': True,
                'project': output_dir,
                'name': 'validation',
                'exist_ok': True
            }
            
            # 执行验证
            results = self.model.val(**val_args)
            
            # 提取验证指标
            metrics = self._extract_validation_metrics(results)
            
            # 构建结果
            result_data = {
                'success': True,
                'model_path': self.model_path,
                'dataset_path': self.dataset_path,
                'metrics': metrics,
                'validation_args': val_args
            }
            
            # 保存结果
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                result_file = os.path.join(output_dir, 'validation_results.json')
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, indent=2, ensure_ascii=False)
                logger.info(f"验证结果保存到: {result_file}")
            
            logger.info("模型验证完成")
            return result_data
            
        except Exception as e:
            logger.error(f"模型验证失败: {e}")
            error_result = {
                'success': False,
                'error_message': str(e),
                'model_path': self.model_path,
                'dataset_path': self.dataset_path
            }
            
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                result_file = os.path.join(output_dir, 'validation_results.json')
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(error_result, f, indent=2, ensure_ascii=False)
            
            return error_result
    
    def _extract_validation_metrics(self, results) -> Dict[str, Any]:
        """提取验证指标"""
        try:
            metrics = {}
            
            if hasattr(results, 'results_dict'):
                results_dict = results.results_dict
                
                # 提取主要指标
                metrics.update({
                    'map50': float(results_dict.get('metrics/mAP50(B)', 0.0)),
                    'map50_95': float(results_dict.get('metrics/mAP50-95(B)', 0.0)),
                    'precision': float(results_dict.get('metrics/precision(B)', 0.0)),
                    'recall': float(results_dict.get('metrics/recall(B)', 0.0)),
                    'f1_score': 0.0,  # 计算F1分数
                    'fitness': float(results_dict.get('fitness', 0.0))
                })
                
                # 计算F1分数
                if metrics['precision'] > 0 and metrics['recall'] > 0:
                    metrics['f1_score'] = 2 * (metrics['precision'] * metrics['recall']) / (metrics['precision'] + metrics['recall'])
            
            # 如果有类别特定的指标
            if hasattr(results, 'maps'):
                class_maps = results.maps
                if class_maps is not None and len(class_maps) > 0:
                    metrics['class_maps'] = [float(m) for m in class_maps]
                    metrics['mean_class_map'] = float(class_maps.mean()) if len(class_maps) > 0 else 0.0
            
            # 添加其他有用的指标
            if hasattr(results, 'box'):
                box_metrics = results.box
                if hasattr(box_metrics, 'mp'):
                    metrics['mean_precision'] = float(box_metrics.mp)
                if hasattr(box_metrics, 'mr'):
                    metrics['mean_recall'] = float(box_metrics.mr)
                if hasattr(box_metrics, 'map50'):
                    metrics['box_map50'] = float(box_metrics.map50)
                if hasattr(box_metrics, 'map'):
                    metrics['box_map50_95'] = float(box_metrics.map)
            
            return metrics
            
        except Exception as e:
            logger.warning(f"提取验证指标失败: {e}")
            return {
                'map50': 0.0,
                'map50_95': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'fitness': 0.0
            }
    
    def benchmark(self, output_dir: str = None) -> Dict[str, Any]:
        """执行模型基准测试"""
        try:
            logger.info("开始模型基准测试")
            
            # 执行基准测试
            results = self.model.benchmark(
                data=self.dataset_path,
                imgsz=640,
                half=False,
                device='auto',
                verbose=True
            )
            
            benchmark_data = {
                'success': True,
                'model_path': self.model_path,
                'benchmark_results': str(results) if results else 'No results'
            }
            
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                result_file = os.path.join(output_dir, 'benchmark_results.json')
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(benchmark_data, f, indent=2, ensure_ascii=False)
                logger.info(f"基准测试结果保存到: {result_file}")
            
            return benchmark_data
            
        except Exception as e:
            logger.error(f"基准测试失败: {e}")
            return {
                'success': False,
                'error_message': str(e),
                'model_path': self.model_path
            }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv11 模型验证')
    parser.add_argument('--model', required=True, help='模型文件路径')
    parser.add_argument('--data', required=True, help='数据集路径或配置文件')
    parser.add_argument('--output', help='输出目录')
    parser.add_argument('--imgsz', type=int, default=640, help='图像尺寸')
    parser.add_argument('--batch', type=int, default=16, help='批次大小')
    parser.add_argument('--conf', type=float, default=0.001, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.6, help='IoU阈值')
    parser.add_argument('--benchmark', action='store_true', help='执行基准测试')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.model):
        logger.error(f"模型文件不存在: {args.model}")
        sys.exit(1)
    
    if not os.path.exists(args.data):
        logger.error(f"数据集文件不存在: {args.data}")
        sys.exit(1)
    
    # 创建验证器
    validator = YOLOv11Validator(args.model, args.data)
    
    if args.benchmark:
        # 执行基准测试
        result = validator.benchmark(args.output)
    else:
        # 执行验证
        result = validator.validate(
            output_dir=args.output,
            image_size=args.imgsz,
            batch_size=args.batch,
            confidence_threshold=args.conf,
            iou_threshold=args.iou
        )
    
    # 输出结果
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if not result.get('success', False):
        sys.exit(1)

if __name__ == '__main__':
    main()
