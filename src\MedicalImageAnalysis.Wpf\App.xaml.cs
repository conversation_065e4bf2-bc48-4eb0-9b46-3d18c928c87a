﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using MaterialDesignThemes.Wpf;
using MaterialDesignColors;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Wpf.Services;

namespace MedicalImageAnalysis.Wpf;

/// <summary>
/// 医学影像解析系统 WPF 应用程序
/// </summary>
public partial class App : System.Windows.Application
{
    private IHost? _host;
    private IServiceProvider? _serviceProvider;
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 设置全局异常处理
            SetupGlobalExceptionHandling();

            // 配置 Serilog
            ConfigureLogging();

            // 初始化 MaterialDesign 主题
            InitializeMaterialDesign();

            // 配置依赖注入
            ConfigureDependencyInjection();

            // 创建并显示主窗口
            CreateMainWindow();

            Log.Information("医学影像解析系统启动成功");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }

    /// <summary>
    /// 设置全局异常处理
    /// </summary>
    private void SetupGlobalExceptionHandling()
    {
        // 处理UI线程异常
        DispatcherUnhandledException += (sender, e) =>
        {
            Log.Error(e.Exception, "UI线程发生未处理的异常");
            MessageBox.Show($"发生未处理的异常：{e.Exception.Message}", "错误",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        };

        // 处理非UI线程异常
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            var exception = e.ExceptionObject as Exception;
            Log.Fatal(exception, "应用程序域发生未处理的异常");

            if (e.IsTerminating)
            {
                MessageBox.Show($"应用程序即将终止：{exception?.Message}", "严重错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        };

        // 处理Task异常
        TaskScheduler.UnobservedTaskException += (sender, e) =>
        {
            Log.Error(e.Exception, "Task发生未观察到的异常");
            e.SetObserved();
        };
    }

    /// <summary>
    /// 配置日志记录
    /// </summary>
    private void ConfigureLogging()
    {
        // 确保日志目录存在
        var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        if (!Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .WriteTo.File(
                Path.Combine(logDirectory, "medical-image-analysis-.txt"),
                rollingInterval: Serilog.RollingInterval.Day,
                retainedFileCountLimit: 30,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        Log.Information("医学影像解析系统启动中...");
        Log.Information("应用程序版本: {Version}", System.Reflection.Assembly.GetExecutingAssembly().GetName().Version);
        Log.Information("运行环境: {Environment}", Environment.OSVersion);
        Log.Information("工作目录: {WorkingDirectory}", Environment.CurrentDirectory);
    }

    /// <summary>
    /// 初始化 MaterialDesign 主题
    /// </summary>
    private void InitializeMaterialDesign()
    {
        try
        {
            // 设置 MaterialDesign 主题
            var paletteHelper = new PaletteHelper();
            var theme = paletteHelper.GetTheme();

            // 设置主色调为蓝色
            theme.SetPrimaryColor(Colors.Blue);
            theme.SetSecondaryColor(Colors.LightBlue);

            // 设置为浅色主题
            theme.SetBaseTheme(BaseTheme.Light);

            paletteHelper.SetTheme(theme);

            Log.Information("MaterialDesign 主题初始化完成");
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "MaterialDesign 主题初始化失败，使用默认主题");
        }
    }

    /// <summary>
    /// 配置依赖注入
    /// </summary>
    private void ConfigureDependencyInjection()
    {
        var hostBuilder = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json",
                                 optional: true, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册核心服务
                services.AddSingleton<IYoloService, YoloServicePlaceholder>();

                // 注册窗口
                services.AddTransient<MainWindow>();

                // 注册其他服务
                services.AddHttpClient();

                Log.Information("依赖注入配置完成");
            });

        _host = hostBuilder.Build();
        _serviceProvider = _host.Services;
    }

    /// <summary>
    /// 创建主窗口
    /// </summary>
    private void CreateMainWindow()
    {
        if (_serviceProvider == null)
        {
            throw new InvalidOperationException("服务提供程序未初始化");
        }

        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();

        Log.Information("主窗口创建完成");
    }

    protected override void OnExit(ExitEventArgs e)
    {
        try
        {
            Log.Information("应用程序正在关闭...");

            // 停止主机
            _host?.StopAsync(TimeSpan.FromSeconds(5)).Wait();
            _host?.Dispose();

            Log.Information("应用程序关闭完成");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "应用程序关闭时发生错误");
        }
        finally
        {
            Log.CloseAndFlush();
        }

        base.OnExit(e);
    }
}

/// <summary>
/// YOLO 服务的占位符实现，用于演示
/// </summary>
public class YoloServicePlaceholder : IYoloService
{
    private readonly ILogger<YoloServicePlaceholder> _logger;

    public YoloServicePlaceholder(ILogger<YoloServicePlaceholder> logger)
    {
        _logger = logger;
    }

    public Task<TrainingResult> TrainModelAsync(YoloTrainingConfig trainingConfig, IProgress<TrainingProgress>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 训练功能尚未实现");
        return Task.FromResult(new TrainingResult
        {
            Success = false,
            ErrorMessage = "YOLO 训练功能尚未实现"
        });
    }

    public Task<ValidationResult> ValidateModelAsync(string modelPath, string validationDataPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 验证功能尚未实现");
        return Task.FromResult(new ValidationResult
        {
            Success = false,
            ErrorMessage = "YOLO 验证功能尚未实现"
        });
    }

    public Task<List<Detection>> InferAsync(string modelPath, byte[] imageData, YoloInferenceConfig inferenceConfig, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 推理功能尚未实现");

        // 返回模拟检测结果
        var detections = new List<Detection>
        {
            new Detection
            {
                Label = "示例检测",
                Confidence = 0.85,
                ClassId = 0,
                BoundingBox = new BoundingBox
                {
                    CenterX = 0.5,
                    CenterY = 0.5,
                    Width = 0.2,
                    Height = 0.2
                }
            }
        };

        return Task.FromResult(detections);
    }

    public Task<List<BatchDetectionResult>> BatchInferAsync(string modelPath, IEnumerable<string> imagePaths, YoloInferenceConfig inferenceConfig, IProgress<int>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 批量推理功能尚未实现");
        return Task.FromResult(new List<BatchDetectionResult>());
    }

    public Task<string> ExportModelAsync(string modelPath, ModelExportFormat exportFormat, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 模型导出功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<YoloModelInfo> GetModelInfoAsync(string modelPath)
    {
        _logger.LogWarning("YOLO 模型信息获取功能尚未实现");
        return Task.FromResult(new YoloModelInfo
        {
            Name = "示例模型",
            Version = "1.0.0",
            InputSize = (640, 640),
            ClassCount = 1,
            ClassNames = { "示例类别" }
        });
    }

    public Task<string> CreateDatasetConfigAsync(DatasetConfig datasetConfig, string outputPath)
    {
        _logger.LogWarning("YOLO 数据集配置创建功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<DatasetValidationResult> ValidateDatasetAsync(string datasetPath)
    {
        _logger.LogWarning("YOLO 数据集验证功能尚未实现");
        return Task.FromResult(new DatasetValidationResult
        {
            IsValid = false,
            Errors = { "YOLO 数据集验证功能尚未实现" }
        });
    }

    public Task<string> GenerateDataAugmentationAsync(string sourceDataPath, DataAugmentationConfig augmentationConfig, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 数据增强功能尚未实现");
        return Task.FromResult(string.Empty);
    }
}

