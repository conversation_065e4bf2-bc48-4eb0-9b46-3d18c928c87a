using System.Linq.Expressions;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 通用仓储接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IRepository<T> where T : class
{
    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有实体
    /// </summary>
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据条件查找实体
    /// </summary>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据条件查找单个实体
    /// </summary>
    Task<T?> FindFirstAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 分页查询
    /// </summary>
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加实体
    /// </summary>
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量添加实体
    /// </summary>
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新实体
    /// </summary>
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除实体
    /// </summary>
    Task DeleteAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    Task DeleteByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除实体
    /// </summary>
    Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取实体数量
    /// </summary>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存更改
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 患者仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.Patient> Patients { get; }

    /// <summary>
    /// DICOM研究仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.DicomStudy> Studies { get; }

    /// <summary>
    /// DICOM序列仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.DicomSeries> Series { get; }

    /// <summary>
    /// DICOM实例仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.DicomInstance> Instances { get; }

    /// <summary>
    /// 标注仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.Annotation> Annotations { get; }

    /// <summary>
    /// 标注项目仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.AnnotationProject> AnnotationProjects { get; }

    /// <summary>
    /// 标注模板仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.AnnotationTemplate> AnnotationTemplates { get; }

    /// <summary>
    /// 训练任务仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.TrainingJob> TrainingJobs { get; }

    /// <summary>
    /// 训练数据集仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.TrainingDataset> TrainingDatasets { get; }

    /// <summary>
    /// 模型版本仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.ModelVersion> ModelVersions { get; }

    /// <summary>
    /// 训练指标仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.TrainingMetric> TrainingMetrics { get; }

    /// <summary>
    /// 处理任务仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.ProcessingTask> ProcessingTasks { get; }

    /// <summary>
    /// 处理结果仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.ProcessingResult> ProcessingResults { get; }

    /// <summary>
    /// 处理日志仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.ProcessingLog> ProcessingLogs { get; }

    /// <summary>
    /// 系统配置仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.SystemConfiguration> SystemConfigurations { get; }

    /// <summary>
    /// 用户偏好仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.UserPreference> UserPreferences { get; }

    /// <summary>
    /// 审计日志仓储
    /// </summary>
    IRepository<MedicalImageAnalysis.Core.Entities.AuditLog> AuditLogs { get; }

    /// <summary>
    /// 开始事务
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存所有更改
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
