#!/bin/bash

# 医学影像解析系统部署脚本
# 支持Docker Compose和Kubernetes部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
医学影像解析系统部署脚本

用法: $0 [选项] <命令>

命令:
  docker-build    构建Docker镜像
  docker-up       启动Docker Compose服务
  docker-down     停止Docker Compose服务
  docker-logs     查看Docker服务日志
  k8s-deploy      部署到Kubernetes
  k8s-delete      从Kubernetes删除
  k8s-status      查看Kubernetes状态
  health-check    执行健康检查
  backup          备份数据
  restore         恢复数据

选项:
  -h, --help      显示此帮助信息
  -v, --verbose   详细输出
  -e, --env       指定环境 (dev|staging|prod)
  --no-cache      构建时不使用缓存
  --force         强制执行操作

示例:
  $0 docker-build --no-cache
  $0 docker-up -e prod
  $0 k8s-deploy -v
  $0 health-check
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi
    
    if [[ "$DEPLOY_TARGET" == "k8s" ]]; then
        if ! command -v kubectl &> /dev/null; then
            missing_deps+=("kubectl")
        fi
        
        if ! command -v helm &> /dev/null; then
            missing_deps+=("helm")
        fi
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建Docker镜像
docker_build() {
    log_info "构建Docker镜像..."
    
    local build_args=""
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="--no-cache"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        build_args="$build_args --progress=plain"
    fi
    
    docker build $build_args -t medical-image-analysis:latest .
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        docker tag medical-image-analysis:latest medical-image-analysis:$(date +%Y%m%d-%H%M%S)
    fi
    
    log_success "Docker镜像构建完成"
}

# 启动Docker Compose服务
docker_up() {
    log_info "启动Docker Compose服务..."
    
    local compose_file="docker-compose.yml"
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        compose_file="docker-compose.prod.yml"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        compose_file="docker-compose.staging.yml"
    fi
    
    if [[ ! -f "$compose_file" ]]; then
        log_warning "未找到 $compose_file，使用默认配置"
        compose_file="docker-compose.yml"
    fi
    
    docker-compose -f "$compose_file" up -d
    
    log_success "Docker Compose服务已启动"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 执行健康检查
    health_check
}

# 停止Docker Compose服务
docker_down() {
    log_info "停止Docker Compose服务..."
    
    docker-compose down
    
    if [[ "$FORCE" == "true" ]]; then
        log_warning "强制清理Docker资源..."
        docker system prune -f
        docker volume prune -f
    fi
    
    log_success "Docker Compose服务已停止"
}

# 查看Docker服务日志
docker_logs() {
    log_info "查看Docker服务日志..."
    
    if [[ -n "$SERVICE_NAME" ]]; then
        docker-compose logs -f "$SERVICE_NAME"
    else
        docker-compose logs -f
    fi
}

# 部署到Kubernetes
k8s_deploy() {
    log_info "部署到Kubernetes..."
    
    # 检查kubectl连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 创建命名空间
    kubectl apply -f k8s/namespace.yaml
    
    # 应用配置
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/secrets.yaml
    
    # 部署数据库
    kubectl apply -f k8s/database/
    
    # 等待数据库就绪
    log_info "等待数据库就绪..."
    kubectl wait --for=condition=ready pod -l app=sqlserver -n medical-image-analysis --timeout=300s
    
    # 部署应用
    kubectl apply -f k8s/app/
    
    # 等待应用就绪
    log_info "等待应用就绪..."
    kubectl wait --for=condition=ready pod -l app=medical-image-analysis -n medical-image-analysis --timeout=300s
    
    # 部署Ingress
    kubectl apply -f k8s/ingress/
    
    log_success "Kubernetes部署完成"
    
    # 显示访问信息
    k8s_status
}

# 从Kubernetes删除
k8s_delete() {
    log_info "从Kubernetes删除..."
    
    if [[ "$FORCE" == "true" ]]; then
        kubectl delete namespace medical-image-analysis --force --grace-period=0
    else
        kubectl delete -f k8s/ --recursive
    fi
    
    log_success "Kubernetes资源已删除"
}

# 查看Kubernetes状态
k8s_status() {
    log_info "Kubernetes状态:"
    
    echo
    echo "=== Pods ==="
    kubectl get pods -n medical-image-analysis -o wide
    
    echo
    echo "=== Services ==="
    kubectl get services -n medical-image-analysis
    
    echo
    echo "=== Ingress ==="
    kubectl get ingress -n medical-image-analysis
    
    echo
    echo "=== PVC ==="
    kubectl get pvc -n medical-image-analysis
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local api_url="http://localhost:5000"
    local web_url="http://localhost:5001"
    
    if [[ "$DEPLOY_TARGET" == "k8s" ]]; then
        # 获取Kubernetes服务地址
        api_url=$(kubectl get service medical-api-service -n medical-image-analysis -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [[ -z "$api_url" ]]; then
            api_url="http://localhost:5000"
        else
            api_url="http://$api_url:5000"
        fi
    fi
    
    # 检查API健康状态
    if curl -f "$api_url/health" &> /dev/null; then
        log_success "API服务健康检查通过"
    else
        log_error "API服务健康检查失败"
        return 1
    fi
    
    # 检查Web健康状态
    if curl -f "$web_url/health" &> /dev/null; then
        log_success "Web服务健康检查通过"
    else
        log_error "Web服务健康检查失败"
        return 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    local backup_dir="backups/$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [[ "$DEPLOY_TARGET" == "docker" ]]; then
        # Docker备份
        docker-compose exec sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q "BACKUP DATABASE MedicalImageAnalysisDb TO DISK = '/var/opt/mssql/backup/medical_db.bak'"
        docker cp medical-sqlserver:/var/opt/mssql/backup/medical_db.bak "$backup_dir/"
        
        # 备份数据卷
        docker run --rm -v medical_data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/data.tar.gz -C /data .
    else
        # Kubernetes备份
        kubectl exec -n medical-image-analysis deployment/sqlserver -- /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q "BACKUP DATABASE MedicalImageAnalysisDb TO DISK = '/var/opt/mssql/backup/medical_db.bak'"
        kubectl cp medical-image-analysis/sqlserver-pod:/var/opt/mssql/backup/medical_db.bak "$backup_dir/medical_db.bak"
    fi
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    log_info "恢复数据..."
    
    if [[ -z "$BACKUP_PATH" ]]; then
        log_error "请指定备份路径: --backup-path <path>"
        exit 1
    fi
    
    if [[ ! -f "$BACKUP_PATH/medical_db.bak" ]]; then
        log_error "备份文件不存在: $BACKUP_PATH/medical_db.bak"
        exit 1
    fi
    
    # 恢复数据库
    if [[ "$DEPLOY_TARGET" == "docker" ]]; then
        docker cp "$BACKUP_PATH/medical_db.bak" medical-sqlserver:/var/opt/mssql/backup/
        docker-compose exec sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q "RESTORE DATABASE MedicalImageAnalysisDb FROM DISK = '/var/opt/mssql/backup/medical_db.bak' WITH REPLACE"
    else
        kubectl cp "$BACKUP_PATH/medical_db.bak" medical-image-analysis/sqlserver-pod:/var/opt/mssql/backup/
        kubectl exec -n medical-image-analysis deployment/sqlserver -- /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q "RESTORE DATABASE MedicalImageAnalysisDb FROM DISK = '/var/opt/mssql/backup/medical_db.bak' WITH REPLACE"
    fi
    
    log_success "数据恢复完成"
}

# 解析命令行参数
VERBOSE=false
ENVIRONMENT="dev"
NO_CACHE=false
FORCE=false
DEPLOY_TARGET="docker"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --backup-path)
            BACKUP_PATH="$2"
            shift 2
            ;;
        --service)
            SERVICE_NAME="$2"
            shift 2
            ;;
        docker-build|docker-up|docker-down|docker-logs)
            COMMAND="$1"
            DEPLOY_TARGET="docker"
            shift
            ;;
        k8s-deploy|k8s-delete|k8s-status)
            COMMAND="$1"
            DEPLOY_TARGET="k8s"
            shift
            ;;
        health-check|backup|restore)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查命令
if [[ -z "$COMMAND" ]]; then
    log_error "请指定命令"
    show_help
    exit 1
fi

# 主执行逻辑
main() {
    log_info "医学影像解析系统部署脚本"
    log_info "环境: $ENVIRONMENT"
    log_info "部署目标: $DEPLOY_TARGET"
    log_info "命令: $COMMAND"
    
    check_dependencies
    
    case $COMMAND in
        docker-build)
            docker_build
            ;;
        docker-up)
            docker_up
            ;;
        docker-down)
            docker_down
            ;;
        docker-logs)
            docker_logs
            ;;
        k8s-deploy)
            k8s_deploy
            ;;
        k8s-delete)
            k8s_delete
            ;;
        k8s-status)
            k8s_status
            ;;
        health-check)
            health_check
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data
            ;;
        *)
            log_error "未知命令: $COMMAND"
            exit 1
            ;;
    esac
}

# 执行主函数
main
