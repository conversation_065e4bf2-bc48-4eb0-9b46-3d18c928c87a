2025-07-23 08:36:31.532 +08:00 [INF] 启动医学影像解析系统 Web 应用
2025-07-23 08:36:31.606 +08:00 [INF] Now listening on: http://localhost:5002
2025-07-23 08:36:31.612 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:36:31.614 +08:00 [INF] Hosting environment: Development
2025-07-23 08:36:31.615 +08:00 [INF] Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web
2025-07-23 08:37:08.799 +08:00 [WRN] Failed to determine the https port for redirect.
2025-07-23 08:37:08.813 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
[2025-07-23 08:44:30.799 +08:00 INF] : 启动医学影像解析系统 Web 应用 {}
[2025-07-23 08:44:30.865 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5002 {"EventId":{"Id":14,"Name":"ListeningOnAddress"}}
[2025-07-23 08:44:30.871 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down. {}
[2025-07-23 08:44:30.873 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Development {}
[2025-07-23 08:44:30.874 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web {}
[2025-07-23 08:46:32.650 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"RequestId":"0HNE9G9F9C315:********","RequestPath":"/","ConnectionId":"0HNE9G9F9C315"}
[2025-07-23 08:46:32.663 +08:00 ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request. {"EventId":{"Id":1,"Name":"UnhandledException"},"RequestId":"0HNE9G9F9C315:********","RequestPath":"/","ConnectionId":"0HNE9G9F9C315"}
System.InvalidOperationException: Cannot find the fallback endpoint specified by route values: { page: /_Host, area:  }.
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
