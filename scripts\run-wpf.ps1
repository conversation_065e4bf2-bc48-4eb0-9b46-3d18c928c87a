# 医学影像解析系统 - WPF客户端启动脚本

param(
    [string]$Configuration = "Debug",
    [switch]$Build = $false,
    [switch]$Clean = $false,
    [switch]$Verbose = $false,
    [string]$ApiUrl = "http://localhost:5000"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::<PERSON>an
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# 检查先决条件
function Test-Prerequisites {
    Write-Info "检查先决条件..."
    
    # 检查.NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Info ".NET SDK 版本: $dotnetVersion"
    }
    catch {
        Write-Error "未找到 .NET SDK，请安装 .NET 8 SDK"
        exit 1
    }
    
    # 检查项目文件
    $projectPath = "src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj"
    if (-not (Test-Path $projectPath)) {
        Write-Error "未找到WPF项目文件: $projectPath"
        exit 1
    }
    
    Write-Success "先决条件检查通过"
}

# 清理项目
function Invoke-Clean {
    Write-Info "清理项目..."
    
    try {
        dotnet clean "src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj" --configuration $Configuration
        
        # 删除bin和obj目录
        $binPath = "src/MedicalImageAnalysis.WpfClient/bin"
        $objPath = "src/MedicalImageAnalysis.WpfClient/obj"
        
        if (Test-Path $binPath) {
            Remove-Item $binPath -Recurse -Force
            Write-Info "已删除 $binPath"
        }
        
        if (Test-Path $objPath) {
            Remove-Item $objPath -Recurse -Force
            Write-Info "已删除 $objPath"
        }
        
        Write-Success "项目清理完成"
    }
    catch {
        Write-Error "清理项目失败: $($_.Exception.Message)"
        exit 1
    }
}

# 还原NuGet包
function Invoke-Restore {
    Write-Info "还原NuGet包..."
    
    try {
        $restoreArgs = @(
            "restore"
            "src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj"
        )
        
        if ($Verbose) {
            $restoreArgs += "--verbosity", "detailed"
        }
        
        & dotnet $restoreArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet restore 失败，退出代码: $LASTEXITCODE"
        }
        
        Write-Success "NuGet包还原完成"
    }
    catch {
        Write-Error "还原NuGet包失败: $($_.Exception.Message)"
        exit 1
    }
}

# 构建项目
function Invoke-Build {
    Write-Info "构建WPF项目..."
    
    try {
        $buildArgs = @(
            "build"
            "src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj"
            "--configuration", $Configuration
            "--no-restore"
        )
        
        if ($Verbose) {
            $buildArgs += "--verbosity", "detailed"
        }
        
        & dotnet $buildArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet build 失败，退出代码: $LASTEXITCODE"
        }
        
        Write-Success "项目构建完成"
    }
    catch {
        Write-Error "构建项目失败: $($_.Exception.Message)"
        exit 1
    }
}

# 检查API服务
function Test-ApiService {
    Write-Info "检查API服务连接..."
    
    try {
        $response = Invoke-WebRequest -Uri "$ApiUrl/health" -Method GET -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "API服务连接正常: $ApiUrl"
            return $true
        }
        else {
            Write-Warning "API服务响应异常: HTTP $($response.StatusCode)"
            return $false
        }
    }
    catch {
        Write-Warning "无法连接到API服务: $ApiUrl"
        Write-Warning "错误: $($_.Exception.Message)"
        Write-Info "WPF客户端将在离线模式下启动"
        return $false
    }
}

# 更新配置文件
function Update-Configuration {
    param([string]$ApiUrl)
    
    Write-Info "更新配置文件..."
    
    $configPath = "src/MedicalImageAnalysis.WpfClient/appsettings.json"
    
    try {
        if (Test-Path $configPath) {
            $config = Get-Content $configPath -Raw | ConvertFrom-Json
            $config.ApiSettings.BaseUrl = $ApiUrl
            $config.ApiSettings.SignalRUrl = "$ApiUrl/hubs/processing"
            
            $config | ConvertTo-Json -Depth 10 | Set-Content $configPath -Encoding UTF8
            Write-Success "配置文件已更新: API URL = $ApiUrl"
        }
        else {
            Write-Warning "配置文件不存在: $configPath"
        }
    }
    catch {
        Write-Warning "更新配置文件失败: $($_.Exception.Message)"
    }
}

# 运行WPF应用
function Start-WpfApplication {
    Write-Info "启动WPF应用程序..."
    
    try {
        $runArgs = @(
            "run"
            "--project", "src/MedicalImageAnalysis.WpfClient/MedicalImageAnalysis.WpfClient.csproj"
            "--configuration", $Configuration
            "--no-build"
        )
        
        if ($Verbose) {
            $runArgs += "--verbosity", "detailed"
        }
        
        Write-Success "WPF应用程序正在启动..."
        Write-Info "配置: $Configuration"
        Write-Info "API URL: $ApiUrl"
        Write-Info ""
        Write-Info "按 Ctrl+C 停止应用程序"
        Write-Info ""
        
        & dotnet $runArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "应用程序启动失败，退出代码: $LASTEXITCODE"
        }
    }
    catch {
        Write-Error "启动WPF应用程序失败: $($_.Exception.Message)"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
医学影像解析系统 - WPF客户端启动脚本

用法: .\run-wpf.ps1 [参数]

参数:
  -Configuration <Debug|Release>  构建配置 (默认: Debug)
  -Build                         强制重新构建
  -Clean                         清理后构建
  -Verbose                       详细输出
  -ApiUrl <URL>                  API服务地址 (默认: http://localhost:5000)
  -Help                          显示此帮助信息

示例:
  .\run-wpf.ps1                                    # 使用默认设置运行
  .\run-wpf.ps1 -Configuration Release             # 使用Release配置运行
  .\run-wpf.ps1 -Build -Verbose                    # 强制构建并显示详细输出
  .\run-wpf.ps1 -ApiUrl "http://*************:5000" # 连接到远程API服务
  .\run-wpf.ps1 -Clean -Build                      # 清理并重新构建

注意:
  - 确保已安装 .NET 8 SDK
  - 如果API服务未运行，应用程序将在离线模式下启动
  - 可以通过应用程序设置界面修改API连接地址
"@
}

# 主函数
function Main {
    Write-Info "医学影像解析系统 - WPF客户端启动脚本"
    Write-Info "================================================"
    
    # 检查帮助参数
    if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
        Show-Help
        return
    }
    
    try {
        # 检查先决条件
        Test-Prerequisites
        
        # 清理项目（如果需要）
        if ($Clean) {
            Invoke-Clean
        }
        
        # 还原NuGet包
        Invoke-Restore
        
        # 构建项目（如果需要）
        if ($Build -or $Clean) {
            Invoke-Build
        }
        
        # 检查API服务
        Test-ApiService
        
        # 更新配置
        Update-Configuration -ApiUrl $ApiUrl
        
        # 启动应用程序
        Start-WpfApplication
        
    }
    catch {
        Write-Error "脚本执行失败: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main
