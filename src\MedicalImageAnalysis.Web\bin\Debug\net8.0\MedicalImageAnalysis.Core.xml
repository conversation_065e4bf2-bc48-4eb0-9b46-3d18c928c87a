<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MedicalImageAnalysis.Core</name>
    </assembly>
    <members>
        <member name="T:MedicalImageAnalysis.Core.Entities.Annotation">
            <summary>
            标注实体，表示在医学影像上的标注信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Id">
            <summary>
            标注唯一标识符
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Type">
            <summary>
            标注类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Label">
            <summary>
            标注标签/类别
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Description">
            <summary>
            标注描述
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Confidence">
            <summary>
            置信度 (0.0 - 1.0)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.BoundingBox">
            <summary>
            边界框坐标 (归一化坐标 0.0-1.0)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.PolygonPoints">
            <summary>
            多边形坐标点 (用于精确分割)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Source">
            <summary>
            标注来源
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.CreatedBy">
            <summary>
            创建者
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.VerifiedBy">
            <summary>
            验证者
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.VerificationStatus">
            <summary>
            验证状态
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.InstanceId">
            <summary>
            所属实例ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Instance">
            <summary>
            所属实例
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.VerifiedAt">
            <summary>
            验证时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Annotation.Area">
            <summary>
            获取标注的面积 (归一化)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.Annotation.ToYoloFormat(System.Int32)">
            <summary>
            转换为 YOLO 格式
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.Annotation.ToCocoFormat(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            转换为 COCO 格式
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.Annotation.CalculatePolygonArea">
            <summary>
            计算多边形面积
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.BoundingBox">
            <summary>
            边界框
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.CenterX">
            <summary>
            中心点 X 坐标 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.CenterY">
            <summary>
            中心点 Y 坐标 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Width">
            <summary>
            宽度 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Height">
            <summary>
            高度 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Left">
            <summary>
            左上角 X 坐标
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Top">
            <summary>
            左上角 Y 坐标
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Right">
            <summary>
            右下角 X 坐标
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BoundingBox.Bottom">
            <summary>
            右下角 Y 坐标
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.BoundingBox.ToAbsolute(System.Int32,System.Int32)">
            <summary>
            转换为绝对坐标
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AbsoluteBoundingBox">
            <summary>
            绝对坐标边界框
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.Point2D">
            <summary>
            2D 点
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationType">
            <summary>
            标注类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationSource">
            <summary>
            标注来源枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.VerificationStatus">
            <summary>
            验证状态枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.Detection">
            <summary>
            标注相关的扩展实体和配置类
            </summary>
            <summary>
            检测结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Detection.Label">
            <summary>
            检测标签
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Detection.Confidence">
            <summary>
            置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Detection.BoundingBox">
            <summary>
            边界框
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.Detection.ClassId">
            <summary>
            类别ID
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.BatchDetectionResult">
            <summary>
            批量检测结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BatchDetectionResult.ImagePath">
            <summary>
            图像路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BatchDetectionResult.Detections">
            <summary>
            检测结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BatchDetectionResult.ProcessingTimeMs">
            <summary>
            处理时间 (毫秒)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BatchDetectionResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.BatchDetectionResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.YoloModelInfo">
            <summary>
            YOLO 模型信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.Name">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.Version">
            <summary>
            模型版本
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.InputSize">
            <summary>
            输入尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.ClassCount">
            <summary>
            类别数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.ClassNames">
            <summary>
            类别名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.FileSize">
            <summary>
            模型文件大小 (字节)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.YoloModelInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DatasetConfig">
            <summary>
            数据集配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetConfig.TrainPath">
            <summary>
            训练集路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetConfig.ValidationPath">
            <summary>
            验证集路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetConfig.TestPath">
            <summary>
            测试集路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetConfig.ClassCount">
            <summary>
            类别数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetConfig.ClassNames">
            <summary>
            类别名称
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DatasetValidationResult">
            <summary>
            数据集验证结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.Errors">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.Warnings">
            <summary>
            警告信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.TrainImageCount">
            <summary>
            训练集图像数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.ValidationImageCount">
            <summary>
            验证集图像数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.TestImageCount">
            <summary>
            测试集图像数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetValidationResult.TotalAnnotationCount">
            <summary>
            总标注数量
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.TrainingAugmentationConfig">
            <summary>
            训练增强配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.TrainingAugmentationConfig.MosaicProbability">
            <summary>
            Mosaic 增强概率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.TrainingAugmentationConfig.MixUpProbability">
            <summary>
            MixUp 增强概率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.TrainingAugmentationConfig.CutMixProbability">
            <summary>
            CutMix 增强概率
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.Map50">
            <summary>
            mAP@0.5
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.Map5095">
            <summary>
            mAP@0.5:0.95
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.Precision">
            <summary>
            精确率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.Recall">
            <summary>
            召回率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.F1Score">
            <summary>
            F1 分数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.ClassAP">
            <summary>
            每个类别的 AP
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ValidationResult.ValidationTimeSeconds">
            <summary>
            验证耗时 (秒)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ExportProgress">
            <summary>
            导出进度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ExportProgress.CurrentFileIndex">
            <summary>
            当前处理的文件索引
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ExportProgress.TotalFiles">
            <summary>
            总文件数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ExportProgress.CurrentFileName">
            <summary>
            当前文件名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ExportProgress.ProgressPercentage">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DatasetExportResult">
            <summary>
            数据集导出结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.OutputPath">
            <summary>
            输出路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.ExportedImageCount">
            <summary>
            导出的图像数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.ExportedAnnotationCount">
            <summary>
            导出的标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DatasetExportResult.ExportTimeSeconds">
            <summary>
            导出耗时 (秒)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationStatistics">
            <summary>
            标注统计信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.TotalAnnotations">
            <summary>
            总标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.AverageConfidence">
            <summary>
            平均置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.MinConfidence">
            <summary>
            最小置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.MaxConfidence">
            <summary>
            最大置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.ClassDistribution">
            <summary>
            类别分布
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.SourceDistribution">
            <summary>
            来源分布
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.TypeDistribution">
            <summary>
            类型分布
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.AverageBoundingBoxArea">
            <summary>
            平均边界框面积
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.MinBoundingBoxArea">
            <summary>
            最小边界框面积
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.MaxBoundingBoxArea">
            <summary>
            最大边界框面积
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationStatistics.VerificationStatusDistribution">
            <summary>
            验证状态分布
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly">
            <summary>
            标注异常
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly.AnnotationId">
            <summary>
            标注ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly.Type">
            <summary>
            异常类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly.Description">
            <summary>
            异常描述
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly.Severity">
            <summary>
            严重程度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationAnomaly.DetectedAt">
            <summary>
            检测时间
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig">
            <summary>
            异常检测配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig.ConfidenceThreshold">
            <summary>
            置信度阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig.SizeAnomalySensitivity">
            <summary>
            尺寸异常检测敏感度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig.OverlapThreshold">
            <summary>
            重叠检测阈值
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation">
            <summary>
            标注推荐
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation.RecommendedLabel">
            <summary>
            推荐的标签
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation.RecommendedBoundingBox">
            <summary>
            推荐的边界框
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation.Confidence">
            <summary>
            推荐置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation.Reason">
            <summary>
            推荐原因
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendation.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig">
            <summary>
            标注推荐配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig.MinConfidenceThreshold">
            <summary>
            最小置信度阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig.MaxRecommendations">
            <summary>
            最大推荐数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig.EnableSimilarityBasedRecommendation">
            <summary>
            是否基于相似性推荐
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig">
            <summary>
            标注合并配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig.OverlapThreshold">
            <summary>
            重叠阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig.Strategy">
            <summary>
            合并策略
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig.KeepOriginalAnnotations">
            <summary>
            是否保留原始标注
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnomalyType">
            <summary>
            异常类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnomalySeverity">
            <summary>
            异常严重程度枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.RecommendationPriority">
            <summary>
            推荐优先级枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.MergeStrategy">
            <summary>
            合并策略枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DicomInstance">
            <summary>
            DICOM 实例实体，表示单个影像切片
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.Id">
            <summary>
            实例唯一标识符
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.SopInstanceUid">
            <summary>
            DICOM SOP 实例 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.SopClassUid">
            <summary>
            SOP 类 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.TransferSyntaxUid">
            <summary>
            传输语法 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.InstanceNumber">
            <summary>
            实例号
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.Rows">
            <summary>
            图像行数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.Columns">
            <summary>
            图像列数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.PixelSpacing">
            <summary>
            像素间距 (X, Y)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.SliceThickness">
            <summary>
            切片厚度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.SliceLocation">
            <summary>
            切片位置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.ImagePosition">
            <summary>
            图像位置 (X, Y, Z)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.ImageOrientationPatient">
            <summary>
            图像方向余弦
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.WindowWidth">
            <summary>
            窗宽
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.WindowCenter">
            <summary>
            窗位
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.RescaleSlope">
            <summary>
            重缩放斜率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.RescaleIntercept">
            <summary>
            重缩放截距
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.PixelDataType">
            <summary>
            像素数据类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.BitsAllocated">
            <summary>
            每像素位数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.BitsStored">
            <summary>
            存储位数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.HighBit">
            <summary>
            高位
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.PixelRepresentation">
            <summary>
            像素表示 (0=无符号, 1=有符号)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.PhotometricInterpretation">
            <summary>
            光度解释
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.FilePath">
            <summary>
            原始文件路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.FileSize">
            <summary>
            文件大小 (字节)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.FileHash">
            <summary>
            文件哈希值 (用于去重)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.SeriesId">
            <summary>
            所属序列ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.Series">
            <summary>
            所属序列
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.Annotations">
            <summary>
            标注集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.PixelDataSize">
            <summary>
            获取像素数据的字节大小
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.IsSignedPixelData">
            <summary>
            检查是否为有符号像素数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomInstance.HounsfieldConversion">
            <summary>
            获取 Hounsfield 单位转换参数
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.DicomInstance.GetPixelValueRange">
            <summary>
            计算实际的像素值范围
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.DicomInstance.GetRecommendedWindow">
            <summary>
            获取推荐的窗宽窗位设置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DicomSeries">
            <summary>
            DICOM 序列实体，表示一个影像序列
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.Id">
            <summary>
            序列唯一标识符
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesInstanceUid">
            <summary>
            DICOM 序列实例 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesDescription">
            <summary>
            序列描述
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.Modality">
            <summary>
            模态类型 (CT, MR, US, etc.)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesDateTime">
            <summary>
            序列日期时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesDate">
            <summary>
            序列日期
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SeriesTime">
            <summary>
            序列时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.BodyPartExamined">
            <summary>
            检查部位
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.StudyId">
            <summary>
            所属研究ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.Study">
            <summary>
            所属研究
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.Instances">
            <summary>
            包含的实例集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.ImageSize">
            <summary>
            获取序列的图像尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.SliceCount">
            <summary>
            获取序列的切片数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.IsComplete">
            <summary>
            检查序列是否完整
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomSeries.PixelDataType">
            <summary>
            获取序列的像素数据类型
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.PixelDataType">
            <summary>
            像素数据类型枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.Unknown">
            <summary>
            未知类型
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.UnsignedInt8">
            <summary>
            无符号8位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.SignedInt8">
            <summary>
            有符号8位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.UnsignedInt16">
            <summary>
            无符号16位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.SignedInt16">
            <summary>
            有符号16位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.UnsignedInt32">
            <summary>
            无符号32位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.SignedInt32">
            <summary>
            有符号32位整数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.Float32">
            <summary>
            32位浮点数
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Entities.PixelDataType.Float64">
            <summary>
            64位浮点数
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.DicomStudy">
            <summary>
            DICOM 研究实体，表示一次完整的医学影像检查
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.Id">
            <summary>
            研究唯一标识符
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.StudyInstanceUid">
            <summary>
            DICOM 研究实例 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.PatientId">
            <summary>
            患者ID（外键）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.Patient">
            <summary>
            患者信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.StudyDateTime">
            <summary>
            研究日期时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.StudyDate">
            <summary>
            研究日期
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.StudyTime">
            <summary>
            研究时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.AccessionNumber">
            <summary>
            检查号
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.ReferringPhysicianName">
            <summary>
            转诊医生姓名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.StudyDescription">
            <summary>
            研究描述
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.BodyPart">
            <summary>
            检查部位
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.Orientation">
            <summary>
            影像方向
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.Status">
            <summary>
            研究状态
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.Series">
            <summary>
            包含的序列集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.ProcessingResults">
            <summary>
            处理结果集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.UpdatedAt">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.TotalSlices">
            <summary>
            获取研究的总切片数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.PixelSpacing">
            <summary>
            获取研究的像素间距
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.DicomStudy.SliceThickness">
            <summary>
            获取切片厚度
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.PatientInfo">
            <summary>
            患者信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.PatientId">
            <summary>
            患者ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.PatientName">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.PatientAge">
            <summary>
            患者年龄
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.PatientSex">
            <summary>
            患者性别
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.PatientBirthDate">
            <summary>
            患者出生日期
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.PatientInfo.Studies">
            <summary>
            关联的研究集合
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.Gender">
            <summary>
            性别枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ImageOrientation">
            <summary>
            影像方向枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.StudyStatus">
            <summary>
            研究状态枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.Patient">
            <summary>
            患者信息实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ProcessingResult">
            <summary>
            处理结果实体，表示对医学影像的处理结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Id">
            <summary>
            处理结果唯一标识符
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Type">
            <summary>
            处理类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ResultType">
            <summary>
            结果类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Data">
            <summary>
            结果数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.TaskId">
            <summary>
            关联的处理任务ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Task">
            <summary>
            关联的处理任务
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Status">
            <summary>
            处理状态
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Progress">
            <summary>
            处理进度 (0-100)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.StartTime">
            <summary>
            处理开始时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.EndTime">
            <summary>
            处理结束时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ProcessingTimeMs">
            <summary>
            处理耗时 (毫秒)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Parameters">
            <summary>
            处理参数 (JSON 格式)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ResultData">
            <summary>
            处理结果数据 (JSON 格式)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.OutputFiles">
            <summary>
            输出文件路径集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.DetectedObjectsCount">
            <summary>
            检测到的对象数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.AverageConfidence">
            <summary>
            平均置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.StudyId">
            <summary>
            所属研究ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.Study">
            <summary>
            所属研究
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ModelInfo">
            <summary>
            使用的模型信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.ProcessingTimeDisplay">
            <summary>
            获取处理耗时的友好显示
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.IsSuccessful">
            <summary>
            检查处理是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ProcessingResult.IsInProgress">
            <summary>
            检查处理是否正在进行
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.ProcessingResult.MarkAsStarted">
            <summary>
            标记处理开始
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.ProcessingResult.UpdateProgress(System.Int32,System.String)">
            <summary>
            更新处理进度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.ProcessingResult.MarkAsCompleted(System.String)">
            <summary>
            标记处理完成
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.ProcessingResult.MarkAsFailed(System.String)">
            <summary>
            标记处理失败
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Entities.ProcessingResult.MarkAsCancelled">
            <summary>
            标记处理取消
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ModelInfo">
            <summary>
            模型信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.Name">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.Version">
            <summary>
            模型版本
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.Type">
            <summary>
            模型类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.FilePath">
            <summary>
            模型文件路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.Description">
            <summary>
            模型描述
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.TrainingDataset">
            <summary>
            训练数据集信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.PerformanceMetrics">
            <summary>
            模型性能指标 (JSON 格式)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.SupportedClasses">
            <summary>
            支持的类别
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Entities.ModelInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ProcessingType">
            <summary>
            处理类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ProcessingStatus">
            <summary>
            处理状态枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.TrainingJob">
            <summary>
            训练任务实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.TrainingDataset">
            <summary>
            训练数据集实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ModelVersion">
            <summary>
            模型版本实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.TrainingMetric">
            <summary>
            训练指标实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationProject">
            <summary>
            标注项目实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AnnotationTemplate">
            <summary>
            标注模板实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ProcessingTask">
            <summary>
            处理任务实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.ProcessingLog">
            <summary>
            处理日志实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.SystemConfiguration">
            <summary>
            系统配置实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.UserPreference">
            <summary>
            用户偏好设置实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Entities.AuditLog">
            <summary>
            审计日志实体
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService">
            <summary>
            高级图像处理服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.MultiPlanarReconstructionAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.PixelData},MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane,System.Int32,System.Threading.CancellationToken)">
            <summary>
            多平面重建 (MPR)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.AdvancedEdgeDetectionAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod,System.Double,System.Threading.CancellationToken)">
            <summary>
            高级边缘检测
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.MorphologicalOperationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation,MedicalImageAnalysis.Core.Interfaces.StructuringElement,System.Int32,System.Threading.CancellationToken)">
            <summary>
            形态学操作
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.FrequencyDomainFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.FrequencyFilter,System.Double,System.Threading.CancellationToken)">
            <summary>
            频域滤波
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.TextureAnalysisAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.TextureFeatures,System.Threading.CancellationToken)">
            <summary>
            纹理分析
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAdvancedImageProcessingService.ImageRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.RegistrationMethod,MedicalImageAnalysis.Core.Interfaces.RegistrationParameters,System.Threading.CancellationToken)">
            <summary>
            图像配准
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane">
            <summary>
            重建平面枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane.Axial">
            <summary>
            轴位面
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane.Sagittal">
            <summary>
            矢状面
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane.Coronal">
            <summary>
            冠状面
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane.Oblique">
            <summary>
            斜面
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod">
            <summary>
            边缘检测方法枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Sobel">
            <summary>
            Sobel算子
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Canny">
            <summary>
            Canny边缘检测
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Laplacian">
            <summary>
            Laplacian算子
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Roberts">
            <summary>
            Roberts算子
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Prewitt">
            <summary>
            Prewitt算子
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod.Scharr">
            <summary>
            Scharr算子
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation">
            <summary>
            形态学操作枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.Erosion">
            <summary>
            腐蚀操作
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.Dilation">
            <summary>
            膨胀操作
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.Opening">
            <summary>
            开运算
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.Closing">
            <summary>
            闭运算
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.Gradient">
            <summary>
            形态学梯度
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.TopHat">
            <summary>
            顶帽变换
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation.BlackHat">
            <summary>
            黑帽变换
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter">
            <summary>
            频域滤波器枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter.LowPass">
            <summary>
            低通滤波
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter.HighPass">
            <summary>
            高通滤波
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter.BandPass">
            <summary>
            带通滤波
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter.BandStop">
            <summary>
            带阻滤波
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.FrequencyFilter.Notch">
            <summary>
            陷波滤波
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TextureFeatures">
            <summary>
            纹理特征枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.None">
            <summary>
            无纹理特征
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.GLCM">
            <summary>
            灰度共生矩阵特征
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.LBP">
            <summary>
            局部二值模式特征
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.Gabor">
            <summary>
            Gabor滤波器特征
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.Wavelet">
            <summary>
            小波变换特征
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.TextureFeatures.All">
            <summary>
            所有纹理特征
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod">
            <summary>
            配准方法枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod.Rigid">
            <summary>
            刚性配准
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod.Affine">
            <summary>
            仿射配准
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod.Deformable">
            <summary>
            可变形配准
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod.Demons">
            <summary>
            Demons算法
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Core.Interfaces.RegistrationMethod.BSpline">
            <summary>
            B样条配准
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.StructuringElement">
            <summary>
            结构元素
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StructuringElement.Kernel">
            <summary>
            结构元素核
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StructuringElement.Width">
            <summary>
            结构元素宽度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StructuringElement.Height">
            <summary>
            结构元素高度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.StructuringElement.Rectangle(System.Int32,System.Int32)">
            <summary>
            创建矩形结构元素
            </summary>
            <param name="width">宽度</param>
            <param name="height">高度</param>
            <returns>矩形结构元素</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.StructuringElement.Circle(System.Int32)">
            <summary>
            创建圆形结构元素
            </summary>
            <param name="radius">半径</param>
            <returns>圆形结构元素</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters">
            <summary>
            配准参数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.MaxIterations">
            <summary>
            最大迭代次数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.Tolerance">
            <summary>
            收敛容差
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.SimilarityMetric">
            <summary>
            相似性度量方法
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.Optimizer">
            <summary>
            优化器类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.LearningRate">
            <summary>
            学习率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationParameters.AdditionalParameters">
            <summary>
            附加参数
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult">
            <summary>
            多平面重建结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.Success">
            <summary>
            重建是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.Plane">
            <summary>
            重建平面
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.SliceIndex">
            <summary>
            切片索引
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.ReconstructedImage">
            <summary>
            重建后的图像
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.MultiPlanarReconstructionResult.ProcessingTimeMs">
            <summary>
            处理时间（毫秒）
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TextureAnalysisResult">
            <summary>
            纹理分析结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TextureAnalysisResult.GLCMFeatures">
            <summary>
            灰度共生矩阵特征
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TextureAnalysisResult.LBPFeatures">
            <summary>
            局部二值模式特征
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TextureAnalysisResult.GaborFeatures">
            <summary>
            Gabor滤波器特征
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TextureAnalysisResult.WaveletFeatures">
            <summary>
            小波变换特征
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures">
            <summary>
            GLCM特征
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures.Contrast">
            <summary>
            对比度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures.Correlation">
            <summary>
            相关性
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures.Energy">
            <summary>
            能量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures.Homogeneity">
            <summary>
            同质性
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.GLCMFeatures.Entropy">
            <summary>
            熵
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.LBPFeatures">
            <summary>
            LBP特征
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.GaborFeatures">
            <summary>
            Gabor特征
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.WaveletFeatures">
            <summary>
            小波特征
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageRegistrationResult">
            <summary>
            图像配准结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IAnnotationService">
            <summary>
            标注服务接口，提供智能标注和标注管理功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.GenerateAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig,System.Threading.CancellationToken)">
            <summary>
            自动生成标注
            </summary>
            <param name="instance">DICOM 实例</param>
            <param name="annotationConfig">标注配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>生成的标注集合</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.BatchGenerateAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance},MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.AnnotationProgress},System.Threading.CancellationToken)">
            <summary>
            批量自动标注
            </summary>
            <param name="instances">DICOM 实例集合</param>
            <param name="annotationConfig">标注配置</param>
            <param name="progressCallback">进度回调</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>批量标注结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.ValidateAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules)">
            <summary>
            验证标注质量
            </summary>
            <param name="annotations">标注集合</param>
            <param name="validationRules">验证规则</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.OptimizeBoundingBoxAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig)">
            <summary>
            优化标注边界框
            </summary>
            <param name="annotation">原始标注</param>
            <param name="pixelData">像素数据</param>
            <param name="optimizationConfig">优化配置</param>
            <returns>优化后的标注</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.ConvertAnnotationFormatAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.AnnotationFormat,System.ValueTuple{System.Int32,System.Int32},System.Collections.Generic.Dictionary{System.String,System.Int32})">
            <summary>
            转换标注格式
            </summary>
            <param name="annotations">标注集合</param>
            <param name="targetFormat">目标格式</param>
            <param name="imageSize">图像尺寸</param>
            <param name="classMapping">类别映射</param>
            <returns>转换后的标注数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.ExportTrainingDatasetAsync(MedicalImageAnalysis.Core.Entities.DicomStudy,MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig,System.String,System.IProgress{MedicalImageAnalysis.Core.Entities.ExportProgress},System.Threading.CancellationToken)">
            <summary>
            导出训练数据集
            </summary>
            <param name="study">研究实体</param>
            <param name="exportConfig">导出配置</param>
            <param name="outputPath">输出路径</param>
            <param name="progressCallback">进度回调</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.GenerateAnnotationStatisticsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation})">
            <summary>
            生成标注统计信息
            </summary>
            <param name="annotations">标注集合</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.DetectAnnotationAnomaliesAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig)">
            <summary>
            检测标注异常
            </summary>
            <param name="annotations">标注集合</param>
            <param name="detectionConfig">检测配置</param>
            <returns>异常检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.RecommendAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig)">
            <summary>
            智能推荐标注
            </summary>
            <param name="instance">DICOM 实例</param>
            <param name="existingAnnotations">已有标注</param>
            <param name="recommendationConfig">推荐配置</param>
            <returns>推荐的标注</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IAnnotationService.MergeOverlappingAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig)">
            <summary>
            合并重叠标注
            </summary>
            <param name="annotations">标注集合</param>
            <param name="mergeConfig">合并配置</param>
            <returns>合并后的标注</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig">
            <summary>
            自动标注配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.ModelPath">
            <summary>
            使用的模型路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.ConfidenceThreshold">
            <summary>
            置信度阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.IouThreshold">
            <summary>
            IoU 阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.TargetClasses">
            <summary>
            目标类别 (为空则检测所有类别)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.MinBoundingBoxSize">
            <summary>
            最小边界框尺寸 (像素)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.MaxBoundingBoxSize">
            <summary>
            最大边界框尺寸 (像素)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.EnablePostProcessing">
            <summary>
            是否启用后处理优化
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.AutoAdjustWindowLevel">
            <summary>
            是否自动调整窗宽窗位
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig.PreprocessingOptions">
            <summary>
            预处理配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules">
            <summary>
            标注验证规则
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.MinConfidence">
            <summary>
            最小置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.MaxConfidence">
            <summary>
            最大置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.MinBoundingBoxArea">
            <summary>
            最小边界框面积 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.MaxBoundingBoxArea">
            <summary>
            最大边界框面积 (归一化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.AllowedLabels">
            <summary>
            允许的标签集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.CheckBoundingBoxBounds">
            <summary>
            是否检查边界框越界
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.CheckDuplicateAnnotations">
            <summary>
            是否检查重复标注
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules.DuplicateIouThreshold">
            <summary>
            重复检测的 IoU 阈值
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig">
            <summary>
            边界框优化配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig.Method">
            <summary>
            优化方法
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig.ExpansionMargin">
            <summary>
            扩展边距 (像素)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig.UseMorphologicalOperations">
            <summary>
            是否使用形态学操作
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig.EdgeDetectionThreshold">
            <summary>
            边缘检测阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig.MaxIterations">
            <summary>
            最大迭代次数
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig">
            <summary>
            数据集导出配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.Format">
            <summary>
            导出格式
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.TrainRatio">
            <summary>
            训练集比例
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.ValidationRatio">
            <summary>
            验证集比例
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.TestRatio">
            <summary>
            测试集比例
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.ImageFormat">
            <summary>
            图像格式
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.ImageQuality">
            <summary>
            图像质量 (1-100)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.TargetImageSize">
            <summary>
            目标图像尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.MaintainAspectRatio">
            <summary>
            是否保持宽高比
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.IncludeNegativeSamples">
            <summary>
            是否包含负样本 (无标注的图像)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.NegativeSampleRatio">
            <summary>
            负样本比例
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.ClassMapping">
            <summary>
            类别映射
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.GenerateAugmentations">
            <summary>
            是否生成数据增强
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig.AugmentationConfig">
            <summary>
            数据增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress">
            <summary>
            标注进度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.CurrentIndex">
            <summary>
            当前处理的实例索引
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.TotalCount">
            <summary>
            总实例数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.CurrentAnnotationCount">
            <summary>
            当前实例的标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.TotalAnnotationCount">
            <summary>
            总标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.CurrentInstanceUid">
            <summary>
            当前处理的实例 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationProgress.ProgressPercentage">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult">
            <summary>
            批量标注结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.ProcessedInstanceCount">
            <summary>
            处理的实例数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.GeneratedAnnotationCount">
            <summary>
            生成的标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.ProcessingTimeSeconds">
            <summary>
            处理耗时 (秒)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.InstanceAnnotations">
            <summary>
            每个实例的标注结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.BatchAnnotationResult.FailedInstances">
            <summary>
            处理失败的实例
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult">
            <summary>
            标注验证结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult.IsValid">
            <summary>
            是否通过验证
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult.Errors">
            <summary>
            验证错误
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult.Warnings">
            <summary>
            验证警告
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult.ValidAnnotationCount">
            <summary>
            有效标注数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.AnnotationValidationResult.InvalidAnnotationCount">
            <summary>
            无效标注数量
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ValidationError">
            <summary>
            验证错误
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationError.AnnotationId">
            <summary>
            标注 ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationError.Type">
            <summary>
            错误类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationError.Description">
            <summary>
            错误描述
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ValidationWarning">
            <summary>
            验证警告
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationWarning.AnnotationId">
            <summary>
            标注 ID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationWarning.Type">
            <summary>
            警告类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ValidationWarning.Description">
            <summary>
            警告描述
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationFormat">
            <summary>
            标注格式枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DatasetFormat">
            <summary>
            数据集格式枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationMethod">
            <summary>
            边界框优化方法枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ValidationErrorType">
            <summary>
            验证错误类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ValidationWarningType">
            <summary>
            验证警告类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService">
            <summary>
            数据增强服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.AugmentDataAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.TrainingData},MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.AugmentationProgress},System.Threading.CancellationToken)">
            <summary>
            执行数据增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.GeometricAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.GeometricAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            几何变换增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.ImageQualityAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.ImageQualityAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            图像质量增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.NoiseAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.NoiseAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            噪声增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.MedicalSpecificAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.MedicalAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            医学影像特定增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDataAugmentationService.MixupAugmentationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.TrainingData},MedicalImageAnalysis.Core.Interfaces.MixupConfig,System.Threading.CancellationToken)">
            <summary>
            混合增强策略
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TrainingData">
            <summary>
            训练数据
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig">
            <summary>
            数据增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.GeometricAugmentationConfig">
            <summary>
            几何变换增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageQualityAugmentationConfig">
            <summary>
            图像质量增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.NoiseAugmentationConfig">
            <summary>
            噪声增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.MedicalAugmentationConfig">
            <summary>
            医学影像特定增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.MixupConfig">
            <summary>
            混合增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DataAugmentationResult">
            <summary>
            数据增强结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AugmentationProgress">
            <summary>
            增强进度
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IDicomService">
            <summary>
            DICOM 服务接口，提供 DICOM 文件处理的核心功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ParseDicomFilesAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            解析 DICOM 文件并创建研究
            </summary>
            <param name="filePaths">DICOM 文件路径集合</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解析后的研究实体</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ParseDicomFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            解析单个 DICOM 文件
            </summary>
            <param name="filePath">DICOM 文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解析后的实例实体</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ValidateDicomFileAsync(System.String)">
            <summary>
            验证 DICOM 文件的有效性
            </summary>
            <param name="filePath">DICOM 文件路径</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ExtractMetadataAsync(System.String)">
            <summary>
            提取 DICOM 文件的元数据
            </summary>
            <param name="filePath">DICOM 文件路径</param>
            <returns>元数据字典</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.GetPixelDataAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            获取 DICOM 实例的像素数据
            </summary>
            <param name="instance">DICOM 实例</param>
            <param name="applyModalityLut">是否应用模态 LUT</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>像素数据数组</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ConvertToHounsfieldUnitsAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            将像素数据转换为 Hounsfield 单位
            </summary>
            <param name="pixelData">原始像素数据</param>
            <param name="instance">DICOM 实例</param>
            <returns>HU 值数组</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.ApplyWindowLevelAsync(System.Double[],System.Double,System.Double)">
            <summary>
            应用窗宽窗位调整
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="windowWidth">窗宽</param>
            <param name="windowCenter">窗位</param>
            <returns>调整后的像素数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.DetectImageOrientationAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance})">
            <summary>
            检测影像方向
            </summary>
            <param name="instances">实例集合</param>
            <returns>检测到的影像方向</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.SortInstancesAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance})">
            <summary>
            排序 DICOM 实例
            </summary>
            <param name="instances">实例集合</param>
            <returns>排序后的实例集合</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDicomService.CalculateStudyStatisticsAsync(MedicalImageAnalysis.Core.Entities.DicomStudy)">
            <summary>
            计算研究的统计信息
            </summary>
            <param name="study">研究实体</param>
            <returns>统计信息</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult">
            <summary>
            DICOM 验证结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.Errors">
            <summary>
            错误信息集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.Warnings">
            <summary>
            警告信息集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.HasPixelData">
            <summary>
            是否包含像素数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.SopClassUid">
            <summary>
            SOP 类 UID
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DicomValidationResult.TransferSyntaxUid">
            <summary>
            传输语法 UID
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.PixelData">
            <summary>
            像素数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.Data">
            <summary>
            像素数据数组
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.Width">
            <summary>
            图像宽度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.Height">
            <summary>
            图像高度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.BitsPerPixel">
            <summary>
            每像素位数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.IsSigned">
            <summary>
            是否为有符号数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.PixelData.PhotometricInterpretation">
            <summary>
            光度解释
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.PixelData.GetPixel``1(System.Int32,System.Int32)">
            <summary>
            获取指定位置的像素值
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.PixelData.SetPixel``1(System.Int32,System.Int32,``0)">
            <summary>
            设置指定位置的像素值
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.PixelData.ToArray``1">
            <summary>
            转换为指定类型的数组
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.StudyStatistics">
            <summary>
            研究统计信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.TotalSeries">
            <summary>
            总序列数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.TotalInstances">
            <summary>
            总实例数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.TotalFileSize">
            <summary>
            总文件大小 (字节)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.AverageSliceThickness">
            <summary>
            平均切片厚度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.PixelSpacingRange">
            <summary>
            像素间距范围
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.ImageSizeRange">
            <summary>
            图像尺寸范围
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.Modalities">
            <summary>
            模态类型集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.BodyParts">
            <summary>
            检查部位集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.StudyStatistics.OrientationDistribution">
            <summary>
            影像方向分布
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IDirectoryService">
            <summary>
            目录管理服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.GetSystemDirectoriesAsync">
            <summary>
            获取系统目录信息
            </summary>
            <returns>系统目录信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.OpenDirectoryAsync(System.String)">
            <summary>
            打开指定目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>是否成功打开</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.GetDirectoryContentAsync(System.String,System.Boolean)">
            <summary>
            获取目录内容
            </summary>
            <param name="directoryPath">目录路径</param>
            <param name="includeSubdirectories">是否包含子目录</param>
            <returns>目录内容</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.CreateDirectoryAsync(System.String)">
            <summary>
            创建目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>是否成功创建</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.DeleteDirectoryAsync(System.String,System.Boolean)">
            <summary>
            删除目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <param name="recursive">是否递归删除</param>
            <returns>是否成功删除</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.GetDirectorySizeAsync(System.String)">
            <summary>
            获取目录大小
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>目录大小（字节）</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.CleanupTempFilesAsync(System.Int32)">
            <summary>
            清理临时文件
            </summary>
            <param name="olderThanDays">清理多少天前的文件</param>
            <returns>清理的文件数量</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IDirectoryService.GetDiskUsageAsync">
            <summary>
            获取磁盘使用情况
            </summary>
            <returns>磁盘使用情况</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SystemDirectories">
            <summary>
            系统目录信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.DataDirectory">
            <summary>
            数据目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.LogsDirectory">
            <summary>
            日志目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.TempDirectory">
            <summary>
            临时文件目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.OutputDirectory">
            <summary>
            输出目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.ModelsDirectory">
            <summary>
            模型目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.SampleDataDirectory">
            <summary>
            示例数据目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.ConfigDirectory">
            <summary>
            配置文件目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.BackupDirectory">
            <summary>
            备份目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.CacheDirectory">
            <summary>
            缓存目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SystemDirectories.ScriptsDirectory">
            <summary>
            脚本目录
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DirectoryContent">
            <summary>
            目录内容
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.DirectoryPath">
            <summary>
            目录路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.Files">
            <summary>
            文件列表
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.Subdirectories">
            <summary>
            子目录列表
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.TotalFiles">
            <summary>
            总文件数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.TotalDirectories">
            <summary>
            总目录数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.TotalSize">
            <summary>
            总大小（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryContent.LastModified">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.FileInfo">
            <summary>
            文件信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.Name">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.FullPath">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.Size">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.Extension">
            <summary>
            文件扩展名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.LastModified">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.FileType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.FileInfo.IsReadOnly">
            <summary>
            是否为只读
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo">
            <summary>
            目录信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.Name">
            <summary>
            目录名
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.FullPath">
            <summary>
            目录路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.LastModified">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.FileCount">
            <summary>
            子文件数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.SubdirectoryCount">
            <summary>
            子目录数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DirectoryInfo.Size">
            <summary>
            目录大小（字节）
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DiskUsage">
            <summary>
            磁盘使用情况
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DiskUsage.TotalSpace">
            <summary>
            总空间（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DiskUsage.UsedSpace">
            <summary>
            已使用空间（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DiskUsage.FreeSpace">
            <summary>
            可用空间（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DiskUsage.UsagePercentage">
            <summary>
            使用百分比
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DiskUsage.Drives">
            <summary>
            驱动器信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DriveInfo">
            <summary>
            驱动器信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.Name">
            <summary>
            驱动器名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.DriveType">
            <summary>
            驱动器类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.FileSystem">
            <summary>
            文件系统
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.TotalSpace">
            <summary>
            总空间（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.FreeSpace">
            <summary>
            可用空间（字节）
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.DriveInfo.UsagePercentage">
            <summary>
            使用百分比
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService">
            <summary>
            图像处理服务接口，提供医学影像处理的核心功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.PreprocessImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions,System.Threading.CancellationToken)">
            <summary>
            图像预处理
            </summary>
            <param name="pixelData">原始像素数据</param>
            <param name="options">预处理选项</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>预处理后的像素数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.EnhanceImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImageEnhancementType,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            图像增强
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="enhancementType">增强类型</param>
            <param name="parameters">增强参数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>增强后的像素数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.DenoiseImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.DenoiseType,System.Double,System.Threading.CancellationToken)">
            <summary>
            图像降噪
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="denoiseType">降噪类型</param>
            <param name="strength">降噪强度 (0.0-1.0)</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>降噪后的像素数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.SegmentImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.SegmentationType,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            图像分割
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="segmentationType">分割类型</param>
            <param name="parameters">分割参数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>分割结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.RegisterImagesAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.RegistrationType,System.Threading.CancellationToken)">
            <summary>
            图像配准
            </summary>
            <param name="fixedImage">固定图像</param>
            <param name="movingImage">移动图像</param>
            <param name="registrationType">配准类型</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>配准结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.MultiplanarReconstructionAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane,System.Double,System.Threading.CancellationToken)">
            <summary>
            多平面重建 (MPR)
            </summary>
            <param name="volumeData">体数据</param>
            <param name="plane">重建平面</param>
            <param name="thickness">切片厚度</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>重建后的图像</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.ConvertImageFormatAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImageFormat,System.Int32,System.Threading.CancellationToken)">
            <summary>
            图像格式转换
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="targetFormat">目标格式</param>
            <param name="quality">质量参数 (0-100)</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>转换后的图像数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.GenerateThumbnailAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Int32,System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            生成图像缩略图
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="maxWidth">最大宽度</param>
            <param name="maxHeight">最大高度</param>
            <param name="maintainAspectRatio">是否保持宽高比</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>缩略图数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.CalculateImageStatisticsAsync(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            计算图像统计信息
            </summary>
            <param name="pixelData">像素数据</param>
            <returns>图像统计信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IImageProcessingService.AutoAdjustWindowLevelAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            自动调整窗宽窗位
            </summary>
            <param name="pixelData">像素数据</param>
            <param name="percentile">百分位数 (默认 1% 和 99%)</param>
            <returns>推荐的窗宽窗位</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions">
            <summary>
            图像预处理选项
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.Normalize">
            <summary>
            是否进行归一化
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.NormalizationRange">
            <summary>
            归一化范围
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.Resize">
            <summary>
            是否调整大小
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.TargetSize">
            <summary>
            目标尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.InterpolationMethod">
            <summary>
            插值方法
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.HistogramEqualization">
            <summary>
            是否进行直方图均衡化
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.ApplyClahe">
            <summary>
            是否应用 CLAHE (对比度限制自适应直方图均衡化)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions.ClaheParameters">
            <summary>
            CLAHE 参数
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ClaheParameters">
            <summary>
            CLAHE 参数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ClaheParameters.ClipLimit">
            <summary>
            对比度限制
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ClaheParameters.TileGridSize">
            <summary>
            网格大小
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.VolumeData">
            <summary>
            体数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.VolumeData.Data">
            <summary>
            体素数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.VolumeData.Dimensions">
            <summary>
            体积尺寸 (X, Y, Z)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.VolumeData.Spacing">
            <summary>
            体素间距 (X, Y, Z)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.VolumeData.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SegmentationResult">
            <summary>
            分割结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentationResult.Mask">
            <summary>
            分割掩码
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentationResult.Regions">
            <summary>
            分割区域集合
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentationResult.Confidence">
            <summary>
            分割置信度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentationResult.ProcessingTimeMs">
            <summary>
            处理时间 (毫秒)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion">
            <summary>
            分割区域
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion.Label">
            <summary>
            区域标签
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion.Name">
            <summary>
            区域名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion.Area">
            <summary>
            区域面积 (像素数)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion.BoundingBox">
            <summary>
            边界框
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.SegmentedRegion.Contour">
            <summary>
            轮廓点集合
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.RegistrationResult">
            <summary>
            配准结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationResult.RegisteredImage">
            <summary>
            配准后的图像
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationResult.TransformMatrix">
            <summary>
            变换矩阵
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationResult.RegistrationError">
            <summary>
            配准误差
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationResult.Iterations">
            <summary>
            迭代次数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.RegistrationResult.ProcessingTimeMs">
            <summary>
            处理时间 (毫秒)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageStatistics">
            <summary>
            图像统计信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.Min">
            <summary>
            最小值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.Max">
            <summary>
            最大值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.Mean">
            <summary>
            平均值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.StandardDeviation">
            <summary>
            标准差
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.Median">
            <summary>
            中位数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.Histogram">
            <summary>
            直方图
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.NonZeroPixelCount">
            <summary>
            非零像素数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.ImageStatistics.TotalPixelCount">
            <summary>
            总像素数量
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageEnhancementType">
            <summary>
            图像增强类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DenoiseType">
            <summary>
            降噪类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SegmentationType">
            <summary>
            分割类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.RegistrationType">
            <summary>
            配准类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.InterpolationMethod">
            <summary>
            插值方法枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageFormat">
            <summary>
            图像格式枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService">
            <summary>
            实时通知服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendTrainingProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.TrainingProgressInfo)">
            <summary>
            发送训练进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendTrainingCompletedAsync(System.String,MedicalImageAnalysis.Core.Interfaces.TrainingResult)">
            <summary>
            发送训练完成通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendTrainingFailedAsync(System.String,System.String)">
            <summary>
            发送训练失败通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendProcessingProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.ProcessingProgressInfo)">
            <summary>
            发送处理进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendProcessingCompletedAsync(System.String,MedicalImageAnalysis.Core.Entities.ProcessingResult)">
            <summary>
            发送处理完成通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendSystemStatusAsync(MedicalImageAnalysis.Core.Interfaces.SystemStatusInfo)">
            <summary>
            发送系统状态更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendNotificationAsync(MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送通用通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendNotificationToUserAsync(System.String,MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送给特定用户的通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendNotificationToGroupAsync(System.String,MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送给特定组的通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendDicomUploadProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.DicomUploadProgressInfo)">
            <summary>
            发送DICOM上传进度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendAnnotationProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.AnnotationProgressInfo)">
            <summary>
            发送标注进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRealTimeNotificationService.SendInferenceResultAsync(System.String,MedicalImageAnalysis.Core.Interfaces.InferenceResult)">
            <summary>
            发送模型推理结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TrainingProgressInfo">
            <summary>
            训练进度信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ProcessingProgressInfo">
            <summary>
            处理进度信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SystemStatusInfo">
            <summary>
            系统状态信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.NotificationInfo">
            <summary>
            通知信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.DicomUploadProgressInfo">
            <summary>
            DICOM上传进度信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationProgressInfo">
            <summary>
            标注进度信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.InferenceResult">
            <summary>
            推理结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ISystemMonitoringService">
            <summary>
            实时监控服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISystemMonitoringService.StartMonitoringAsync">
            <summary>
            开始监控
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISystemMonitoringService.StopMonitoringAsync">
            <summary>
            停止监控
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISystemMonitoringService.GetCurrentStatusAsync">
            <summary>
            获取当前系统状态
            </summary>
        </member>
        <member name="E:MedicalImageAnalysis.Core.Interfaces.ISystemMonitoringService.StatusUpdated">
            <summary>
            系统状态更新事件
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService">
            <summary>
            连接管理服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService.GetOnlineUserCount">
            <summary>
            获取在线用户数
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService.GetActiveConnectionCount">
            <summary>
            获取活跃连接数
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService.GetUserConnectionsAsync(System.String)">
            <summary>
            获取用户连接信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService.DisconnectUserAsync(System.String)">
            <summary>
            断开用户所有连接
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IConnectionManagerService.BroadcastMaintenanceNotificationAsync(System.String,System.DateTime)">
            <summary>
            广播系统维护通知
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ConnectionInfo">
            <summary>
            连接信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IRepository`1">
            <summary>
            通用仓储接口
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            根据ID获取实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            获取所有实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.FindFirstAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找单个实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.GetPagedAsync(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            分页查询
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            添加实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量添加实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            更新实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.DeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            删除实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.DeleteByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            根据ID删除实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.DeleteRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量删除实体
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            检查实体是否存在
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            获取实体数量
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IRepository`1.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            保存更改
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork">
            <summary>
            工作单元接口
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.Patients">
            <summary>
            患者仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.Studies">
            <summary>
            DICOM研究仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.Series">
            <summary>
            DICOM序列仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.Instances">
            <summary>
            DICOM实例仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.Annotations">
            <summary>
            标注仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.AnnotationProjects">
            <summary>
            标注项目仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.AnnotationTemplates">
            <summary>
            标注模板仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.TrainingJobs">
            <summary>
            训练任务仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.TrainingDatasets">
            <summary>
            训练数据集仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.ModelVersions">
            <summary>
            模型版本仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.TrainingMetrics">
            <summary>
            训练指标仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.ProcessingTasks">
            <summary>
            处理任务仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.ProcessingResults">
            <summary>
            处理结果仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.ProcessingLogs">
            <summary>
            处理日志仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.SystemConfigurations">
            <summary>
            系统配置仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.UserPreferences">
            <summary>
            用户偏好仓储
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.AuditLogs">
            <summary>
            审计日志仓储
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.BeginTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            开始事务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.CommitTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            提交事务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.RollbackTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            回滚事务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IUnitOfWork.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            保存所有更改
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService">
            <summary>
            智能标注服务接口
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService.GenerateSmartAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.SmartAnnotationConfig,System.Threading.CancellationToken)">
            <summary>
            智能标注生成
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService.AdaptiveAnnotationOptimizationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AdaptiveOptimizationConfig,System.Threading.CancellationToken)">
            <summary>
            自适应标注优化
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService.AutoEvaluateAnnotationQualityAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.QualityEvaluationConfig,System.Threading.CancellationToken)">
            <summary>
            标注质量自动评估
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService.GenerateSmartRecommendationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.SmartRecommendationConfig,System.Threading.CancellationToken)">
            <summary>
            智能标注推荐
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.ISmartAnnotationService.AugmentAnnotationDataAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AnnotationAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            标注数据增强
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SmartAnnotationConfig">
            <summary>
            智能标注配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationFusionConfig">
            <summary>
            标注融合配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.PostProcessingConfig">
            <summary>
            后处理配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SmartFilterConfig">
            <summary>
            智能过滤配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AdaptiveOptimizationConfig">
            <summary>
            自适应优化配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.QualityEvaluationConfig">
            <summary>
            质量评估配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SmartRecommendationConfig">
            <summary>
            智能推荐配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationAugmentationConfig">
            <summary>
            标注增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ImageAugmentationConfig">
            <summary>
            图像增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SyntheticGenerationConfig">
            <summary>
            合成生成配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.FusionMethod">
            <summary>
            融合方法枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.SmartAnnotationResult">
            <summary>
            智能标注结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationQualityScore">
            <summary>
            标注质量分数
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationQualityReport">
            <summary>
            标注质量报告
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AnnotationAugmentationResult">
            <summary>
            标注增强结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData">
            <summary>
            增强标注数据
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.IYoloService">
            <summary>
            YOLO 服务接口，提供 YOLOv11 模型的训练、验证和推理功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.TrainModelAsync(MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.TrainingProgress},System.Threading.CancellationToken)">
            <summary>
            训练 YOLO 模型
            </summary>
            <param name="trainingConfig">训练配置</param>
            <param name="progressCallback">进度回调</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>训练结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.ValidateModelAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            验证模型性能
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="validationDataPath">验证数据路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.InferAsync(System.String,System.Byte[],MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig,System.Threading.CancellationToken)">
            <summary>
            使用模型进行推理
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="imageData">图像数据</param>
            <param name="inferenceConfig">推理配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.BatchInferAsync(System.String,System.Collections.Generic.IEnumerable{System.String},MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig,System.IProgress{System.Int32},System.Threading.CancellationToken)">
            <summary>
            批量推理
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="imagePaths">图像路径集合</param>
            <param name="inferenceConfig">推理配置</param>
            <param name="progressCallback">进度回调</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>批量检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.ExportModelAsync(System.String,MedicalImageAnalysis.Core.Interfaces.ModelExportFormat,System.String,System.Threading.CancellationToken)">
            <summary>
            导出模型为不同格式
            </summary>
            <param name="modelPath">源模型路径</param>
            <param name="exportFormat">导出格式</param>
            <param name="outputPath">输出路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>导出的模型路径</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.GetModelInfoAsync(System.String)">
            <summary>
            获取模型信息
            </summary>
            <param name="modelPath">模型文件路径</param>
            <returns>模型信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.CreateDatasetConfigAsync(MedicalImageAnalysis.Core.Entities.DatasetConfig,System.String)">
            <summary>
            创建数据集配置文件
            </summary>
            <param name="datasetConfig">数据集配置</param>
            <param name="outputPath">输出路径</param>
            <returns>配置文件路径</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.ValidateDatasetAsync(System.String)">
            <summary>
            验证数据集格式
            </summary>
            <param name="datasetPath">数据集路径</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Core.Interfaces.IYoloService.GenerateDataAugmentationAsync(System.String,MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig,System.String,System.Threading.CancellationToken)">
            <summary>
            生成数据增强
            </summary>
            <param name="sourceDataPath">源数据路径</param>
            <param name="augmentationConfig">增强配置</param>
            <param name="outputPath">输出路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>增强后的数据路径</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig">
            <summary>
            YOLO 训练配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.DatasetConfigPath">
            <summary>
            数据集配置文件路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.PretrainedModelPath">
            <summary>
            预训练模型路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.Epochs">
            <summary>
            训练轮数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.BatchSize">
            <summary>
            批次大小
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.ImageSize">
            <summary>
            图像尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.LearningRate">
            <summary>
            学习率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.Optimizer">
            <summary>
            优化器类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.Device">
            <summary>
            设备类型 (CPU/GPU)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.Workers">
            <summary>
            工作线程数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.Patience">
            <summary>
            早停耐心值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.UseMixedPrecision">
            <summary>
            是否使用混合精度训练
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.CacheData">
            <summary>
            是否缓存数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.OutputDirectory">
            <summary>
            输出目录
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.ExperimentName">
            <summary>
            实验名称
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.SaveCheckpoints">
            <summary>
            是否保存检查点
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.ValidationFrequency">
            <summary>
            验证频率 (每 N 个 epoch)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig.AugmentationConfig">
            <summary>
            数据增强配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig">
            <summary>
            YOLO 推理配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.ConfidenceThreshold">
            <summary>
            置信度阈值
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.IouThreshold">
            <summary>
            IoU 阈值 (用于 NMS)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.MaxDetections">
            <summary>
            最大检测数量
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.ImageSize">
            <summary>
            图像尺寸
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.Device">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.UseHalfPrecision">
            <summary>
            是否使用半精度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.UseTensorRT">
            <summary>
            是否启用 TensorRT 优化
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.SaveResults">
            <summary>
            是否保存结果图像
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig.ResultsPath">
            <summary>
            结果保存路径
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TrainingProgress">
            <summary>
            训练进度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.CurrentEpoch">
            <summary>
            当前轮数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.TotalEpochs">
            <summary>
            总轮数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.CurrentBatch">
            <summary>
            当前批次
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.TotalBatches">
            <summary>
            总批次数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.TrainingLoss">
            <summary>
            训练损失
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.ValidationLoss">
            <summary>
            验证损失
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.Map50">
            <summary>
            mAP@0.5
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.Map5095">
            <summary>
            mAP@0.5:0.95
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.LearningRate">
            <summary>
            学习率
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.EstimatedTimeRemaining">
            <summary>
            估计剩余时间 (秒)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingProgress.ProgressPercentage">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TrainingResult">
            <summary>
            训练结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.BestModelPath">
            <summary>
            最佳模型路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.LastModelPath">
            <summary>
            最后模型路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.Metrics">
            <summary>
            训练指标
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.TrainingTimeSeconds">
            <summary>
            训练耗时 (秒)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingResult.OutputDirectory">
            <summary>
            输出目录
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics">
            <summary>
            训练指标
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.BestMap50">
            <summary>
            最佳 mAP@0.5
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.BestMap5095">
            <summary>
            最佳 mAP@0.5:0.95
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.FinalTrainingLoss">
            <summary>
            最终训练损失
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.FinalValidationLoss">
            <summary>
            最终验证损失
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.ConvergedEpoch">
            <summary>
            收敛轮数
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Core.Interfaces.TrainingMetrics.ClassAP">
            <summary>
            每个类别的 AP
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.OptimizerType">
            <summary>
            优化器类型枚举
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Core.Interfaces.ModelExportFormat">
            <summary>
            模型导出格式枚举
            </summary>
        </member>
    </members>
</doc>
