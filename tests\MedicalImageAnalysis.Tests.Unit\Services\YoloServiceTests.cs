using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Infrastructure.Services;
using MedicalImageAnalysis.Tests.Unit.Common;
using Moq;
using Xunit;

namespace MedicalImageAnalysis.Tests.Unit.Services;

/// <summary>
/// YOLO服务单元测试
/// </summary>
public class YoloServiceTests : TestBase
{
    private readonly Mock<ILogger<YoloService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly YoloService _yoloService;

    public YoloServiceTests()
    {
        _mockLogger = new Mock<ILogger<YoloService>>();
        _mockConfiguration = new Mock<IConfiguration>();
        
        // 配置Mock Configuration
        SetupMockConfiguration();
        
        _yoloService = new YoloService(_mockLogger.Object, _mockConfiguration.Object);
    }

    private void SetupMockConfiguration()
    {
        _mockConfiguration.Setup(c => c["MedicalImageAnalysis:Models:ModelDirectory"])
            .Returns("models");
        _mockConfiguration.Setup(c => c["MedicalImageAnalysis:TempDirectory"])
            .Returns("temp");
        _mockConfiguration.Setup(c => c["MedicalImageAnalysis:OutputDirectory"])
            .Returns("output");
    }

    [Fact]
    public async Task TrainModelAsync_ValidConfig_ShouldReturnSuccess()
    {
        // Arrange
        var config = CreateTestEntity<YoloTrainingConfig>();
        config.DatasetPath = TestHelpers.CreateTestImageFile();
        config.Epochs = 1; // 减少训练时间
        
        var progress = new Mock<IProgress<TrainingProgress>>();

        try
        {
            // Act
            var result = await _yoloService.TrainModelAsync(config, progress.Object);

            // Assert
            result.Should().NotBeNull();
            // 注意：由于没有真实的Python环境，这个测试可能会失败
            // 在实际环境中，应该mock Python执行或使用测试替身
        }
        finally
        {
            TestHelpers.CleanupTempFiles(config.DatasetPath);
        }
    }

    [Fact]
    public async Task TrainModelAsync_InvalidConfig_ShouldThrowArgumentException()
    {
        // Arrange
        var config = new YoloTrainingConfig(); // 空配置
        var progress = new Mock<IProgress<TrainingProgress>>();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _yoloService.TrainModelAsync(config, progress.Object));
    }

    [Fact]
    public async Task ValidateModelAsync_ValidModelPath_ShouldReturnSuccess()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");

        try
        {
            // Act
            var result = await _yoloService.ValidateModelAsync(modelPath);

            // Assert
            result.Should().NotBeNull();
            // 注意：实际验证需要真实的YOLO模型文件
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath);
        }
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task ValidateModelAsync_InvalidPath_ShouldThrowArgumentException(string modelPath)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _yoloService.ValidateModelAsync(modelPath));
    }

    [Fact]
    public async Task InferenceAsync_ValidInputs_ShouldReturnDetections()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var imagePath = TestHelpers.CreateTestImageFile(".jpg");
        var config = CreateTestEntity<InferenceConfig>();

        try
        {
            // Act
            var result = await _yoloService.InferenceAsync(modelPath, imagePath, config);

            // Assert
            result.Should().NotBeNull();
            // 注意：实际推理需要真实的模型和图像
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath, imagePath);
        }
    }

    [Fact]
    public async Task InferenceAsync_NonExistentModel_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var modelPath = "non_existent_model.pt";
        var imagePath = TestHelpers.CreateTestImageFile(".jpg");
        var config = CreateTestEntity<InferenceConfig>();

        try
        {
            // Act & Assert
            await Assert.ThrowsAsync<FileNotFoundException>(
                () => _yoloService.InferenceAsync(modelPath, imagePath, config));
        }
        finally
        {
            TestHelpers.CleanupTempFiles(imagePath);
        }
    }

    [Fact]
    public async Task InferenceAsync_NonExistentImage_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var imagePath = "non_existent_image.jpg";
        var config = CreateTestEntity<InferenceConfig>();

        try
        {
            // Act & Assert
            await Assert.ThrowsAsync<FileNotFoundException>(
                () => _yoloService.InferenceAsync(modelPath, imagePath, config));
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath);
        }
    }

    [Fact]
    public async Task ExportModelAsync_ValidInputs_ShouldReturnSuccess()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var outputPath = Path.GetTempFileName();
        var format = ExportFormat.ONNX;

        try
        {
            // Act
            var result = await _yoloService.ExportModelAsync(modelPath, outputPath, format);

            // Assert
            result.Should().NotBeNull();
            // 注意：实际导出需要真实的模型文件
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath, outputPath);
        }
    }

    [Theory]
    [InlineData(ExportFormat.ONNX)]
    [InlineData(ExportFormat.TensorRT)]
    [InlineData(ExportFormat.CoreML)]
    public async Task ExportModelAsync_DifferentFormats_ShouldHandleAll(ExportFormat format)
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var outputPath = Path.GetTempFileName();

        try
        {
            // Act
            var result = await _yoloService.ExportModelAsync(modelPath, outputPath, format);

            // Assert
            result.Should().NotBeNull();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath, outputPath);
        }
    }

    [Fact]
    public async Task GetModelInfoAsync_ValidModel_ShouldReturnInfo()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");

        try
        {
            // Act
            var result = await _yoloService.GetModelInfoAsync(modelPath);

            // Assert
            result.Should().NotBeNull();
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath);
        }
    }

    [Fact]
    public async Task BatchInferenceAsync_MultipleImages_ShouldProcessAll()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var imagePaths = new[]
        {
            TestHelpers.CreateTestImageFile(".jpg"),
            TestHelpers.CreateTestImageFile(".jpg"),
            TestHelpers.CreateTestImageFile(".jpg")
        };
        var config = CreateTestEntity<InferenceConfig>();

        try
        {
            // Act
            var results = await _yoloService.BatchInferenceAsync(modelPath, imagePaths, config);

            // Assert
            results.Should().NotBeNull();
            results.Should().HaveCount(imagePaths.Length);
        }
        finally
        {
            var allFiles = new[] { modelPath }.Concat(imagePaths).ToArray();
            TestHelpers.CleanupTempFiles(allFiles);
        }
    }

    [Fact]
    public async Task CreateDatasetConfigAsync_ValidInputs_ShouldReturnConfig()
    {
        // Arrange
        var datasetPath = Path.GetTempPath();
        var classNames = new[] { "class1", "class2", "class3" };

        // Act
        var result = await _yoloService.CreateDatasetConfigAsync(datasetPath, classNames);

        // Assert
        result.Should().NotBeNull();
        result.Should().Contain("train:");
        result.Should().Contain("val:");
        result.Should().Contain("nc:");
        result.Should().Contain("names:");
    }

    [Theory]
    [InlineData(null)]
    [InlineData(new string[0])]
    public async Task CreateDatasetConfigAsync_InvalidClassNames_ShouldThrowArgumentException(string[] classNames)
    {
        // Arrange
        var datasetPath = Path.GetTempPath();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _yoloService.CreateDatasetConfigAsync(datasetPath, classNames));
    }

    [Fact]
    public async Task ConcurrentInference_ShouldBeThreadSafe()
    {
        // Arrange
        var modelPath = TestHelpers.CreateTestImageFile(".pt");
        var imagePath = TestHelpers.CreateTestImageFile(".jpg");
        var config = CreateTestEntity<InferenceConfig>();
        var concurrentTasks = 5;

        try
        {
            // Act
            var tasks = Enumerable.Range(0, concurrentTasks)
                .Select(_ => _yoloService.InferenceAsync(modelPath, imagePath, config));

            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().HaveCount(concurrentTasks);
            results.Should().OnlyContain(r => r != null);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(modelPath, imagePath);
        }
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _yoloService.Dispose();
        action.Should().NotThrow();
    }
}
