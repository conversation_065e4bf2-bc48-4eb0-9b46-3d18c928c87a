<Window x:Class="MedicalImageAnalysis.Wpf.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="系统设置"
        Height="500"
        Width="600"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        Background="White">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                 Text="系统设置"
                 FontSize="24"
                 FontWeight="Medium"
                 Margin="0,0,0,24"/>

        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 常规设置 -->
                <GroupBox Header="常规设置" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <CheckBox Content="启动时自动检查更新" IsChecked="True" Margin="0,8"/>
                        <CheckBox Content="最小化到系统托盘" IsChecked="False" Margin="0,8"/>
                        <CheckBox Content="启用详细日志记录" IsChecked="True" Margin="0,8"/>

                        <Separator Margin="0,16"/>

                        <StackPanel>
                            <TextBlock Text="界面语言" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="LanguageComboBox" SelectedIndex="0">
                                <ComboBoxItem Content="简体中文"/>
                                <ComboBoxItem Content="English"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- API设置 -->
                <GroupBox Header="API设置" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <StackPanel>
                            <TextBlock Text="API服务器地址" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBox x:Name="ApiUrlTextBox"
                                   Text="http://localhost:5000"/>
                        </StackPanel>

                        <StackPanel Margin="0,16,0,0">
                            <TextBlock Text="连接超时时间（秒）" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Slider x:Name="TimeoutSlider"
                                  Minimum="5"
                                  Maximum="60"
                                  Value="30"
                                  TickFrequency="5"
                                  IsSnapToTickEnabled="True"/>
                            <TextBlock Text="{Binding ElementName=TimeoutSlider, Path=Value, StringFormat=F0}"
                                     HorizontalAlignment="Center"
                                     FontSize="12"
                                     Foreground="Gray"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 处理设置 -->
                <GroupBox Header="处理设置" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <StackPanel>
                            <TextBlock Text="默认置信度阈值" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Slider x:Name="ConfidenceSlider"
                                  Minimum="0.1"
                                  Maximum="1.0"
                                  Value="0.5"
                                  TickFrequency="0.1"
                                  IsSnapToTickEnabled="True"/>
                            <TextBlock Text="{Binding ElementName=ConfidenceSlider, Path=Value, StringFormat=F1}"
                                     HorizontalAlignment="Center"
                                     FontSize="12"
                                     Foreground="Gray"/>
                        </StackPanel>

                        <StackPanel Margin="0,16,0,0">
                            <TextBlock Text="最大并发处理数" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Slider x:Name="ConcurrencySlider"
                                  Minimum="1"
                                  Maximum="8"
                                  Value="2"
                                  TickFrequency="1"
                                  IsSnapToTickEnabled="True"/>
                            <TextBlock Text="{Binding ElementName=ConcurrencySlider, Path=Value, StringFormat=F0}"
                                     HorizontalAlignment="Center"
                                     FontSize="12"
                                     Foreground="Gray"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 存储设置 -->
                <GroupBox Header="存储设置">
                    <StackPanel Margin="16">
                        <StackPanel>
                            <TextBlock Text="数据存储目录" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="DataDirectoryTextBox"
                                       Grid.Column="0"
                                       Text="./data"
                                       Margin="0,0,8,0"/>
                                <Button Grid.Column="1"
                                      Content="浏览"
                                      Background="LightBlue"
                                      Padding="8,4"
                                      Click="BrowseDataDirectory_Click"/>
                            </Grid>
                        </StackPanel>

                        <CheckBox Content="自动清理临时文件" IsChecked="True" Margin="0,16,0,0"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2"
                  Orientation="Horizontal"
                  HorizontalAlignment="Right"
                  Margin="0,24,0,0">
            <Button Content="重置默认"
                  Background="LightGray"
                  Padding="8,4"
                  Margin="0,0,8,0"
                  Click="ResetDefaults_Click"/>
            <Button Content="取消"
                  Background="LightGray"
                  Padding="8,4"
                  Margin="0,0,8,0"
                  Click="Cancel_Click"/>
            <Button Content="保存"
                  Background="DodgerBlue"
                  Foreground="White"
                  Padding="8,4"
                  Click="Save_Click"/>
        </StackPanel>
    </Grid>
</Window>
