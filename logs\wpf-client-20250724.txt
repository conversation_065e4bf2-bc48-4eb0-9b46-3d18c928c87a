2025-07-24 04:02:34.996 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 04:02:35.031 +08:00 [INF] Hosting environment: Production
2025-07-24 04:02:35.032 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 04:02:35.617 +08:00 [ERR] 测试API连接失败
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at MedicalImageAnalysis.WpfClient.Services.ApiService.TestConnectionAsync() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\Services\ApiService.cs:line 53
2025-07-24 04:02:35.638 +08:00 [WRN] 无法连接到API服务
2025-07-24 04:04:00.193 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 04:04:00.225 +08:00 [INF] Hosting environment: Production
2025-07-24 04:04:00.226 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 04:04:00.670 +08:00 [INF] HttpClient BaseAddress: null
2025-07-24 04:04:00.672 +08:00 [ERR] 测试API连接失败
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at MedicalImageAnalysis.WpfClient.Services.ApiService.TestConnectionAsync() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\Services\ApiService.cs:line 54
2025-07-24 04:04:00.682 +08:00 [WRN] 无法连接到API服务
2025-07-24 06:27:11.211 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 06:27:11.239 +08:00 [INF] Hosting environment: Production
2025-07-24 06:27:11.240 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 06:27:11.247 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 06:27:11.662 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 06:27:11.669 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 06:27:11.672 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
