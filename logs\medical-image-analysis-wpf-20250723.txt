2025-07-23 08:24:44.474 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:24:44.511 +08:00 [INF] Hosting environment: Production
2025-07-23 08:24:44.512 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:24:44.829 +08:00 [ERR] 导航到页面时发生错误: HomeMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“51”，行位置为“18”。
 ---> System.Exception: 无法找到名为“MaterialDesignOutlinedCard”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.HomeView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.HomeView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml.cs:line 12
   at MedicalImageAnalysis.Wpf.MainWindow.ShowHomePage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 87
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 42
2025-07-23 08:25:05.012 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:25:05.052 +08:00 [INF] Hosting environment: Production
2025-07-23 08:25:05.053 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:25:05.287 +08:00 [ERR] 导航到页面时发生错误: HomeMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“51”，行位置为“18”。
 ---> System.Exception: 无法找到名为“MaterialDesignOutlinedCard”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.HomeView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.HomeView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml.cs:line 12
   at MedicalImageAnalysis.Wpf.MainWindow.ShowHomePage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 87
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 42
2025-07-23 08:25:23.569 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:25:23.600 +08:00 [INF] Hosting environment: Production
2025-07-23 08:25:23.601 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:25:23.823 +08:00 [ERR] 导航到页面时发生错误: HomeMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“51”，行位置为“18”。
 ---> System.Exception: 无法找到名为“MaterialDesignOutlinedCard”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.HomeView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.HomeView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml.cs:line 12
   at MedicalImageAnalysis.Wpf.MainWindow.ShowHomePage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 87
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 42
2025-07-23 08:27:33.162 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:27:33.195 +08:00 [INF] Hosting environment: Production
2025-07-23 08:27:33.196 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:27:33.430 +08:00 [ERR] 导航到页面时发生错误: HomeMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“51”，行位置为“18”。
 ---> System.Exception: 无法找到名为“MaterialDesignOutlinedCard”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.HomeView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.HomeView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml.cs:line 12
   at MedicalImageAnalysis.Wpf.MainWindow.ShowHomePage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 87
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 42
2025-07-23 08:57:57.554 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:57:57.589 +08:00 [INF] Hosting environment: Production
2025-07-23 08:57:57.589 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:57:57.851 +08:00 [ERR] 导航到页面时发生错误: HomeMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“51”，行位置为“18”。
 ---> System.Exception: 无法找到名为“MaterialDesignOutlinedCard”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.HomeView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.HomeView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\HomeView.xaml.cs:line 12
   at MedicalImageAnalysis.Wpf.MainWindow.ShowHomePage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 87
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 42
2025-07-23 08:59:46.119 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:59:46.152 +08:00 [INF] Hosting environment: Production
2025-07-23 08:59:46.153 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 08:59:46.391 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-23 09:03:32.134 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 09:03:32.184 +08:00 [INF] Hosting environment: Production
2025-07-23 09:03:32.185 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-23 09:03:32.560 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-23 09:04:46.369 +08:00 [INF] Application is shutting down...
2025-07-23 23:42:33.836 +08:00 [INF] 应用程序启动中...
2025-07-23 23:43:24.392 +08:00 [INF] 应用程序启动中...
2025-07-23 23:52:43.025 +08:00 [INF] 应用程序启动中...
2025-07-23 23:52:43.307 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-23 23:53:53.837 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“73”，行位置为“26”。
 ---> System.Exception: 无法找到名为“MaterialDesignRaisedButton”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 24
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 124
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 59
2025-07-23 23:53:58.080 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“73”，行位置为“26”。
 ---> System.Exception: 无法找到名为“MaterialDesignRaisedButton”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 24
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 124
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 59
2025-07-23 23:54:11.848 +08:00 [ERR] 导航到页面时发生错误: DicomUploadMenuItem
System.Windows.Markup.XamlParseException: “在“System.Windows.StaticResourceExtension”上提供值时引发了异常。”，行号为“73”，行位置为“26”。
 ---> System.Exception: 无法找到名为“MaterialDesignRaisedButton”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView.InitializeComponent() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml:line 1
   at MedicalImageAnalysis.Wpf.Views.DicomUploadView..ctor() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\DicomUploadView.xaml.cs:line 24
   at MedicalImageAnalysis.Wpf.MainWindow.ShowDicomUploadPage() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 124
   at MedicalImageAnalysis.Wpf.MainWindow.NavigationListBox_SelectionChanged(Object sender, SelectionChangedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\MainWindow.xaml.cs:line 59
2025-07-23 23:54:14.095 +08:00 [INF] 应用程序正在关闭...
