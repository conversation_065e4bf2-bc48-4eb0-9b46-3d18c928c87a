using Microsoft.EntityFrameworkCore;
using MedicalImageAnalysis.Core.Entities;
using System.Text.Json;

namespace MedicalImageAnalysis.Infrastructure.Data;

/// <summary>
/// 医学影像分析系统数据库上下文
/// </summary>
public class MedicalImageDbContext : DbContext
{
    public MedicalImageDbContext(DbContextOptions<MedicalImageDbContext> options) : base(options)
    {
    }

    // DICOM 相关实体
    public DbSet<DicomStudy> Studies { get; set; }
    public DbSet<DicomSeries> Series { get; set; }
    public DbSet<DicomInstance> Instances { get; set; }
    public DbSet<Patient> Patients { get; set; }

    // 标注相关实体
    public DbSet<Annotation> Annotations { get; set; }
    public DbSet<AnnotationProject> AnnotationProjects { get; set; }
    public DbSet<AnnotationTemplate> AnnotationTemplates { get; set; }

    // 模型训练相关实体
    public DbSet<TrainingJob> TrainingJobs { get; set; }
    public DbSet<TrainingDataset> TrainingDatasets { get; set; }
    public DbSet<ModelVersion> ModelVersions { get; set; }
    public DbSet<TrainingMetric> TrainingMetrics { get; set; }

    // 处理任务相关实体
    public DbSet<ProcessingTask> ProcessingTasks { get; set; }
    public DbSet<ProcessingResult> ProcessingResults { get; set; }
    public DbSet<ProcessingLog> ProcessingLogs { get; set; }

    // 系统配置相关实体
    public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
    public DbSet<UserPreference> UserPreferences { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置Patient实体
        modelBuilder.Entity<Patient>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.PatientId).IsRequired().HasMaxLength(64);
            entity.Property(e => e.PatientName).HasMaxLength(256);
            entity.Property(e => e.PatientBirthDate).HasColumnType("date");
            entity.Property(e => e.PatientSex).HasMaxLength(16);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.HasIndex(e => e.PatientId).IsUnique();
        });

        // 配置DicomStudy实体
        modelBuilder.Entity<DicomStudy>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.StudyInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.StudyDescription).HasMaxLength(256);
            entity.Property(e => e.StudyDate).HasColumnType("date");
            entity.Property(e => e.StudyTime).HasMaxLength(16);
            entity.Property(e => e.AccessionNumber).HasMaxLength(64);
            entity.Property(e => e.ReferringPhysicianName).HasMaxLength(256);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasOne(e => e.Patient)
                  .WithMany(p => p.Studies)
                  .HasForeignKey(e => e.PatientId)
                  .OnDelete(DeleteBehavior.Cascade);
            
            entity.HasIndex(e => e.StudyInstanceUid).IsUnique();
        });

        // 配置DicomSeries实体
        modelBuilder.Entity<DicomSeries>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SeriesInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.SeriesDescription).HasMaxLength(256);
            entity.Property(e => e.Modality).HasMaxLength(16);
            entity.Property(e => e.BodyPartExamined).HasMaxLength(64);
            entity.Property(e => e.SeriesDate).HasColumnType("date");
            entity.Property(e => e.SeriesTime).HasMaxLength(16);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasOne(e => e.Study)
                  .WithMany(s => s.Series)
                  .HasForeignKey(e => e.StudyId)
                  .OnDelete(DeleteBehavior.Cascade);
            
            entity.HasIndex(e => e.SeriesInstanceUid).IsUnique();
        });

        // 配置DicomInstance实体
        modelBuilder.Entity<DicomInstance>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SopInstanceUid).IsRequired().HasMaxLength(64);
            entity.Property(e => e.SopClassUid).HasMaxLength(64);
            entity.Property(e => e.TransferSyntaxUid).HasMaxLength(64);
            entity.Property(e => e.InstanceNumber).HasDefaultValue(1);
            entity.Property(e => e.FilePath).IsRequired().HasMaxLength(512);
            entity.Property(e => e.FileSize).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasOne(e => e.Series)
                  .WithMany(s => s.Instances)
                  .HasForeignKey(e => e.SeriesId)
                  .OnDelete(DeleteBehavior.Cascade);
            
            entity.HasIndex(e => e.SopInstanceUid).IsUnique();
        });

        // 配置Annotation实体
        modelBuilder.Entity<Annotation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Label).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Confidence).HasPrecision(5, 4);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置BoundingBox为拥有实体
            entity.OwnsOne(e => e.BoundingBox, bb =>
            {
                bb.Property(b => b.CenterX).HasPrecision(10, 6);
                bb.Property(b => b.CenterY).HasPrecision(10, 6);
                bb.Property(b => b.Width).HasPrecision(10, 6);
                bb.Property(b => b.Height).HasPrecision(10, 6);
            });
            
            entity.HasOne(e => e.Instance)
                  .WithMany(i => i.Annotations)
                  .HasForeignKey(e => e.InstanceId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置AnnotationProject实体
        modelBuilder.Entity<AnnotationProject>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Settings)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
        });

        // 配置TrainingJob实体
        modelBuilder.Entity<TrainingJob>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.Status).IsRequired().HasMaxLength(32);
            entity.Property(e => e.ModelType).IsRequired().HasMaxLength(64);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Configuration)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
            
            entity.Property(e => e.Results)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
        });

        // 配置TrainingDataset实体
        modelBuilder.Entity<TrainingDataset>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.DatasetType).IsRequired().HasMaxLength(32);
            entity.Property(e => e.DataPath).IsRequired().HasMaxLength(512);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Metadata)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
        });

        // 配置ModelVersion实体
        modelBuilder.Entity<ModelVersion>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Version).IsRequired().HasMaxLength(32);
            entity.Property(e => e.ModelType).IsRequired().HasMaxLength(64);
            entity.Property(e => e.ModelPath).IsRequired().HasMaxLength(512);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Metadata)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
            
            entity.Property(e => e.Metrics)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
            
            entity.HasOne(e => e.TrainingJob)
                  .WithMany(t => t.ModelVersions)
                  .HasForeignKey(e => e.TrainingJobId)
                  .OnDelete(DeleteBehavior.SetNull);
        });

        // 配置ProcessingTask实体
        modelBuilder.Entity<ProcessingTask>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TaskType).IsRequired().HasMaxLength(64);
            entity.Property(e => e.Status).IsRequired().HasMaxLength(32);
            entity.Property(e => e.Priority).HasDefaultValue(0);
            entity.Property(e => e.CreatedBy).HasMaxLength(128);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Parameters)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
        });

        // 配置ProcessingResult实体
        modelBuilder.Entity<ProcessingResult>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ResultType).IsRequired().HasMaxLength(64);
            entity.Property(e => e.Status).IsRequired().HasMaxLength(32);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Data).HasMaxLength(4000);
            
            entity.HasOne(e => e.Task)
                  .WithMany(t => t.Results)
                  .HasForeignKey(e => e.TaskId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置SystemConfiguration实体
        modelBuilder.Entity<SystemConfiguration>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Key).IsRequired().HasMaxLength(128);
            entity.Property(e => e.Value).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.Category).HasMaxLength(64);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasIndex(e => e.Key).IsUnique();
        });

        // 配置AuditLog实体
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Action).IsRequired().HasMaxLength(128);
            entity.Property(e => e.EntityType).HasMaxLength(128);
            entity.Property(e => e.EntityId).HasMaxLength(64);
            entity.Property(e => e.UserId).HasMaxLength(128);
            entity.Property(e => e.UserName).HasMaxLength(256);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(512);
            entity.Property(e => e.Timestamp).HasDefaultValueSql("GETUTCDATE()");
            
            // 配置JSON属性
            entity.Property(e => e.Changes)
                  .HasConversion(
                      v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                      v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
        });
    }
}
