using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using MedicalImageAnalysis.Infrastructure.Data;
using MedicalImageAnalysis.Tests.Unit.Common;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;

namespace MedicalImageAnalysis.Tests.Integration.Api;

/// <summary>
/// DICOM控制器集成测试
/// </summary>
public class DicomControllerTests : IClassFixture<WebApplicationFactory<Program>>, IAsyncDisposable
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public DicomControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureServices(services =>
            {
                // 移除真实数据库
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<MedicalImageDbContext>));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // 添加内存数据库
                services.AddDbContext<MedicalImageDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb");
                });

                // 确保数据库已创建
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<MedicalImageDbContext>();
                context.Database.EnsureCreated();
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetDicomInstances_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/dicom/instances");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task UploadDicomFile_ValidFile_ShouldReturnCreated()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestDicomFile();
        
        try
        {
            using var form = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(await File.ReadAllBytesAsync(testFilePath));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/dicom");
            form.Add(fileContent, "file", "test.dcm");

            // Act
            var response = await _client.PostAsync("/api/dicom/upload", form);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task UploadDicomFile_InvalidFile_ShouldReturnBadRequest()
    {
        // Arrange
        var testFilePath = TestHelpers.CreateTestImageFile(".txt");
        
        try
        {
            using var form = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(await File.ReadAllBytesAsync(testFilePath));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
            form.Add(fileContent, "file", "test.txt");

            // Act
            var response = await _client.PostAsync("/api/dicom/upload", form);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFilePath);
        }
    }

    [Fact]
    public async Task GetDicomInstance_ExistingId_ShouldReturnOk()
    {
        // Arrange
        var instanceId = await CreateTestDicomInstanceAsync();

        // Act
        var response = await _client.GetAsync($"/api/dicom/instances/{instanceId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetDicomInstance_NonExistingId_ShouldReturnNotFound()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/dicom/instances/{nonExistingId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task ConvertToImage_ExistingInstance_ShouldReturnOk()
    {
        // Arrange
        var instanceId = await CreateTestDicomInstanceAsync();

        // Act
        var response = await _client.GetAsync($"/api/dicom/instances/{instanceId}/image");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        response.Content.Headers.ContentType?.MediaType.Should().Be("image/jpeg");
    }

    [Fact]
    public async Task ApplyWindowLevel_ValidParameters_ShouldReturnOk()
    {
        // Arrange
        var instanceId = await CreateTestDicomInstanceAsync();
        var request = new
        {
            WindowWidth = 400,
            WindowCenter = 200
        };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync($"/api/dicom/instances/{instanceId}/window-level", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetDicomTags_ExistingInstance_ShouldReturnOk()
    {
        // Arrange
        var instanceId = await CreateTestDicomInstanceAsync();

        // Act
        var response = await _client.GetAsync($"/api/dicom/instances/{instanceId}/tags");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task DeleteDicomInstance_ExistingId_ShouldReturnNoContent()
    {
        // Arrange
        var instanceId = await CreateTestDicomInstanceAsync();

        // Act
        var response = await _client.DeleteAsync($"/api/dicom/instances/{instanceId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
    }

    [Fact]
    public async Task GetDicomStudies_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/dicom/studies");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetDicomSeries_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/dicom/series");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task SearchDicomInstances_WithQuery_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/dicom/instances?search=test");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetDicomInstancesWithPagination_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/dicom/instances?page=1&pageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task BulkUpload_MultipleFiles_ShouldReturnOk()
    {
        // Arrange
        var testFiles = new[]
        {
            TestHelpers.CreateTestDicomFile(),
            TestHelpers.CreateTestDicomFile(),
            TestHelpers.CreateTestDicomFile()
        };

        try
        {
            using var form = new MultipartFormDataContent();
            
            for (int i = 0; i < testFiles.Length; i++)
            {
                var fileContent = new ByteArrayContent(await File.ReadAllBytesAsync(testFiles[i]));
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/dicom");
                form.Add(fileContent, "files", $"test{i}.dcm");
            }

            // Act
            var response = await _client.PostAsync("/api/dicom/bulk-upload", form);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }
        finally
        {
            TestHelpers.CleanupTempFiles(testFiles);
        }
    }

    [Fact]
    public async Task HealthCheck_ShouldReturnHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Healthy");
    }

    private async Task<Guid> CreateTestDicomInstanceAsync()
    {
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MedicalImageDbContext>();

        var patient = new MedicalImageAnalysis.Core.Entities.Patient
        {
            Id = Guid.NewGuid(),
            PatientId = "TEST001",
            PatientName = "Test Patient",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var study = new MedicalImageAnalysis.Core.Entities.DicomStudy
        {
            Id = Guid.NewGuid(),
            StudyInstanceUid = "*******.5",
            StudyDescription = "Test Study",
            StudyDate = DateTime.Today,
            PatientId = patient.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var series = new MedicalImageAnalysis.Core.Entities.DicomSeries
        {
            Id = Guid.NewGuid(),
            SeriesInstanceUid = "*******.5.6",
            SeriesDescription = "Test Series",
            Modality = "CT",
            StudyId = study.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var instance = new MedicalImageAnalysis.Core.Entities.DicomInstance
        {
            Id = Guid.NewGuid(),
            SopInstanceUid = "*******.5.6.7",
            SopClassUid = "1.2.840.10008.*******.1.2",
            InstanceNumber = 1,
            FilePath = TestHelpers.CreateTestDicomFile(),
            FileSize = 1024,
            SeriesId = series.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Patients.Add(patient);
        context.Studies.Add(study);
        context.Series.Add(series);
        context.Instances.Add(instance);
        
        await context.SaveChangesAsync();

        return instance.Id;
    }

    public async ValueTask DisposeAsync()
    {
        _client?.Dispose();
        await _factory.DisposeAsync();
    }
}
