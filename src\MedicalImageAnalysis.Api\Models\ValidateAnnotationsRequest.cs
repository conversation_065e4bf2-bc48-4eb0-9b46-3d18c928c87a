using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Api.Models;

/// <summary>
/// 验证标注请求模型
/// </summary>
public class ValidateAnnotationsRequest
{
    /// <summary>
    /// 标注数据
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 验证规则
    /// </summary>
    public AnnotationValidationRules? ValidationRules { get; set; }
}

/// <summary>
/// 转换标注格式请求模型
/// </summary>
public class ConvertAnnotationFormatRequest
{
    /// <summary>
    /// 标注数据
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 目标格式
    /// </summary>
    public AnnotationFormat TargetFormat { get; set; }

    /// <summary>
    /// 图像宽度
    /// </summary>
    public int ImageWidth { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int ImageHeight { get; set; }

    /// <summary>
    /// 类别映射
    /// </summary>
    public Dictionary<string, int>? ClassMapping { get; set; }
}

/// <summary>
/// 检测异常请求模型
/// </summary>
public class DetectAnomaliesRequest
{
    /// <summary>
    /// 标注数据
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 异常检测配置
    /// </summary>
    public AnomalyDetectionConfig? DetectionConfig { get; set; }
}

/// <summary>
/// 合并重叠标注请求模型
/// </summary>
public class MergeOverlappingAnnotationsRequest
{
    /// <summary>
    /// 标注数据
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 合并配置
    /// </summary>
    public AnnotationMergeConfig? MergeConfig { get; set; }
}
