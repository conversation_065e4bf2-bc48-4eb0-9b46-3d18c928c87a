is_global = true
build_property.TargetFramework = net8.0
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MedicalImageAnalysis.Web
build_property.RootNamespace = MedicalImageAnalysis.Web
build_property.ProjectDir = D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/About.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Annotation.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQW5ub3RhdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/DatasetExport.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGF0YXNldEV4cG9ydC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/DicomUpload.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGljb21VcGxvYWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/DirectoryManager.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGlyZWN0b3J5TWFuYWdlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Help.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSGVscC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/ImageProcessing.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW1hZ2VQcm9jZXNzaW5nLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/ModelTraining.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTW9kZWxUcmFpbmluZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Settings.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2V0dGluZ3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Statistics.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3RhdGlzdGljcy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Test.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Shared/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/_Host.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0hvc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/AI_project/医学影像解析/src/MedicalImageAnalysis.Web/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
