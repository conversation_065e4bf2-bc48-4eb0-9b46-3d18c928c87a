2025-07-23 08:30:00.713 +08:00 [INF] 启动医学影像解析系统 API
2025-07-23 08:30:00.823 +08:00 [INF] Now listening on: http://localhost:5000
2025-07-23 08:30:00.830 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-23 08:30:00.832 +08:00 [INF] Hosting environment: Production
2025-07-23 08:30:00.833 +08:00 [INF] Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api
2025-07-23 08:30:58.776 +08:00 [WRN] Failed to determine the https port for redirect.
[2025-07-23 08:31:47.788 +08:00 INF] : 启动医学影像解析系统 API {}
[2025-07-23 08:31:47.895 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5000 {"EventId":{"Id":14,"Name":"ListeningOnAddress"}}
[2025-07-23 08:31:47.902 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down. {}
[2025-07-23 08:31:47.903 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Development {}
[2025-07-23 08:31:47.905 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api {}
[2025-07-23 08:32:39.529 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"RequestId":"0HNE9G1MVOJOH:00000001","RequestPath":"/swagger","ConnectionId":"0HNE9G1MVOJOH"}
[2025-07-23 08:33:00.599 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.603 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\logs {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.607 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\temp {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.610 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\output {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.614 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\models {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.618 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\config {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.622 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\backup {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
[2025-07-23 08:33:00.625 +08:00 INF] MedicalImageAnalysis.Infrastructure.Services.DirectoryService: 成功创建目录: D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Api\bin\Debug\net8.0\data\cache {"ActionId":"98e5edb7-276c-481b-a6eb-3d164b19657a","ActionName":"MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories (MedicalImageAnalysis.Api)","RequestId":"0HNE9G1MVOJOJ:00000001","RequestPath":"/api/directory/system-directories","ConnectionId":"0HNE9G1MVOJOJ"}
