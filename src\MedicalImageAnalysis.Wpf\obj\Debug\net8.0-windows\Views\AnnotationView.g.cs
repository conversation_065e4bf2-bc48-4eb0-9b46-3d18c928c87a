﻿#pragma checksum "..\..\..\..\Views\AnnotationView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DD952DC0B7145A3A14D66EAC5093A6E1CC1F284D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// AnnotationView
    /// </summary>
    public partial class AnnotationView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 69 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RectangleToolButton;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CircleToolButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton PolygonToolButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton PointToolButton;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer AnnotationScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AnnotationPlaceholderPanel;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image AnnotationImage;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas AnnotationCanvas;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AnnotationStatusPanel;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnnotationStatusText;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnnotationCountText;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AnnotationCategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomCategoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAIPreAnnotationCheckBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSmartSnapCheckBox;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSimilarRegionSuggestionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ConfidenceThresholdSlider;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView AnnotationListView;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExportFormatComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/annotationview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AnnotationView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 55 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenImage_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RectangleToolButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 73 "..\..\..\..\Views\AnnotationView.xaml"
            this.RectangleToolButton.Click += new System.Windows.RoutedEventHandler(this.AnnotationTool_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CircleToolButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 79 "..\..\..\..\Views\AnnotationView.xaml"
            this.CircleToolButton.Click += new System.Windows.RoutedEventHandler(this.AnnotationTool_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PolygonToolButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 85 "..\..\..\..\Views\AnnotationView.xaml"
            this.PolygonToolButton.Click += new System.Windows.RoutedEventHandler(this.AnnotationTool_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PointToolButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 91 "..\..\..\..\Views\AnnotationView.xaml"
            this.PointToolButton.Click += new System.Windows.RoutedEventHandler(this.AnnotationTool_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 98 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AIDetection_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 111 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearAnnotations_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.AnnotationScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 9:
            this.AnnotationPlaceholderPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.AnnotationImage = ((System.Windows.Controls.Image)(target));
            return;
            case 11:
            this.AnnotationCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 161 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AnnotationCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 162 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.AnnotationCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 163 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AnnotationCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AnnotationStatusPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.AnnotationStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.AnnotationCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.AnnotationCategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.CustomCategoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            
            #line 226 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCategory_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.EnableAIPreAnnotationCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.EnableSmartSnapCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.EnableSimilarRegionSuggestionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.ConfidenceThresholdSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 22:
            this.AnnotationListView = ((System.Windows.Controls.ListView)(target));
            
            #line 267 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AnnotationListView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 301 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditAnnotation_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 304 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteAnnotation_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ExportFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            
            #line 331 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportAnnotations_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 349 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAnnotations_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 353 "..\..\..\..\Views\AnnotationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadAnnotations_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

