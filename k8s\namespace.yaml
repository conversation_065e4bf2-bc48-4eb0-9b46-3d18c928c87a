apiVersion: v1
kind: Namespace
metadata:
  name: medical-image-analysis
  labels:
    name: medical-image-analysis
    app: medical-image-analysis
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: medical-quota
  namespace: medical-image-analysis
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: medical-limits
  namespace: medical-image-analysis
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
