using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// DICOM解析结果
/// </summary>
public class DicomParseResult
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 解析得到的DICOM实例
    /// </summary>
    public DicomInstance? Instance { get; set; }

    /// <summary>
    /// 验证结果
    /// </summary>
    public DicomValidationResult? ValidationResult { get; set; }
}

/// <summary>
/// DICOM验证结果
/// </summary>
public class DicomValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否包含像素数据
    /// </summary>
    public bool HasPixelData { get; set; }

    /// <summary>
    /// SOP类UID
    /// </summary>
    public string? SopClassUid { get; set; }

    /// <summary>
    /// 传输语法UID
    /// </summary>
    public string? TransferSyntaxUid { get; set; }
}

/// <summary>
/// 像素数据
/// </summary>
public class PixelData
{
    /// <summary>
    /// 像素数据数组
    /// </summary>
    public Array Data { get; set; } = null!;

    /// <summary>
    /// 数据类型
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// 图像宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 每像素位数
    /// </summary>
    public int BitsPerPixel { get; set; }

    /// <summary>
    /// 是否为有符号数据
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// 光度解释
    /// </summary>
    public string PhotometricInterpretation { get; set; } = string.Empty;

    /// <summary>
    /// 获取指定位置的像素值
    /// </summary>
    public T GetPixel<T>(int x, int y) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException();

        var index = y * Width + x;
        if (Data is T[] typedArray)
        {
            return typedArray[index];
        }

        return (T)Data.GetValue(index)!;
    }

    /// <summary>
    /// 设置指定位置的像素值
    /// </summary>
    public void SetPixel<T>(int x, int y, T value) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException();

        var index = y * Width + x;
        if (Data is T[] typedArray)
        {
            typedArray[index] = value;
        }
        else
        {
            Data.SetValue(value, index);
        }
    }
}

/// <summary>
/// 批量处理结果
/// </summary>
public class BatchProcessResult
{
    /// <summary>
    /// 总处理数量
    /// </summary>
    public int TotalProcessed { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功处理的文件列表
    /// </summary>
    public List<string> SuccessfulFiles { get; set; } = new();

    /// <summary>
    /// 失败的文件及错误信息
    /// </summary>
    public Dictionary<string, string> FailedFiles { get; set; } = new();

    /// <summary>
    /// 处理成功率
    /// </summary>
    public double SuccessRate => TotalProcessed > 0 ? (double)SuccessCount / TotalProcessed : 0.0;
}

/// <summary>
/// 图像格式枚举
/// </summary>
public enum ImageFormat
{
    Png = 1,
    Jpeg = 2,
    Bmp = 3,
    Tiff = 4
}

/// <summary>
/// DICOM元数据
/// </summary>
public class DicomMetadata
{
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID
    /// </summary>
    public string PatientId { get; set; } = string.Empty;

    /// <summary>
    /// 患者性别
    /// </summary>
    public string PatientSex { get; set; } = string.Empty;

    /// <summary>
    /// 患者出生日期
    /// </summary>
    public DateTime? PatientBirthDate { get; set; }

    /// <summary>
    /// 研究实例UID
    /// </summary>
    public string StudyInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 研究日期
    /// </summary>
    public DateTime? StudyDate { get; set; }

    /// <summary>
    /// 研究时间
    /// </summary>
    public TimeSpan? StudyTime { get; set; }

    /// <summary>
    /// 研究描述
    /// </summary>
    public string StudyDescription { get; set; } = string.Empty;

    /// <summary>
    /// 序列实例UID
    /// </summary>
    public string SeriesInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 序列号
    /// </summary>
    public int SeriesNumber { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    public string SeriesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 模态
    /// </summary>
    public string Modality { get; set; } = string.Empty;

    /// <summary>
    /// SOP实例UID
    /// </summary>
    public string SopInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 实例号
    /// </summary>
    public int InstanceNumber { get; set; }

    /// <summary>
    /// 图像行数
    /// </summary>
    public int Rows { get; set; }

    /// <summary>
    /// 图像列数
    /// </summary>
    public int Columns { get; set; }

    /// <summary>
    /// 像素间距
    /// </summary>
    public (double X, double Y) PixelSpacing { get; set; }

    /// <summary>
    /// 切片厚度
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 切片位置
    /// </summary>
    public double SliceLocation { get; set; }

    /// <summary>
    /// 图像位置
    /// </summary>
    public (double X, double Y, double Z) ImagePosition { get; set; }

    /// <summary>
    /// 图像方向
    /// </summary>
    public double[] ImageOrientationPatient { get; set; } = new double[6];

    /// <summary>
    /// 窗宽
    /// </summary>
    public double WindowWidth { get; set; }

    /// <summary>
    /// 窗位
    /// </summary>
    public double WindowCenter { get; set; }

    /// <summary>
    /// 重缩放斜率
    /// </summary>
    public double RescaleSlope { get; set; } = 1.0;

    /// <summary>
    /// 重缩放截距
    /// </summary>
    public double RescaleIntercept { get; set; } = 0.0;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
