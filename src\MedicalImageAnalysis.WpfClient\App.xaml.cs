using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System.IO;
using System.Windows;
using MedicalImageAnalysis.WpfClient.Services;
using MedicalImageAnalysis.WpfClient.ViewModels;
using MedicalImageAnalysis.WpfClient.Views;

namespace MedicalImageAnalysis.WpfClient;

/// <summary>
/// WPF应用程序主类
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/wpf-client-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // 创建主机
            _host = CreateHostBuilder(e.Args).Build();
            
            // 启动主机
            await _host.StartAsync();

            // 显示主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
        }
        finally
        {
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                // 设置基础路径为应用程序目录
                var basePath = AppDomain.CurrentDomain.BaseDirectory;
                config.SetBasePath(basePath);

                // 添加配置文件
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置服务
                ConfigureServices(services, context.Configuration);
            });

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // HTTP客户端
        services.AddHttpClient<ApiService>(client =>
        {
            client.Timeout = TimeSpan.FromMinutes(5);
        });

        // 应用服务
        services.AddSingleton<IApiService, ApiService>();
        services.AddSingleton<ISignalRService, SignalRService>();
        services.AddSingleton<IFileService, FileService>();
        services.AddSingleton<IDialogService, DialogService>();
        services.AddSingleton<ISettingsService, SettingsService>();
        services.AddSingleton<INotificationService, NotificationService>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();

        // Views
        services.AddTransient<MainWindow>();

        // 其他服务
        services.AddSingleton<IConfiguration>(configuration);
    }
}
