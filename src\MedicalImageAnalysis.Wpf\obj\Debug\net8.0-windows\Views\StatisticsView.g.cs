﻿#pragma checksum "..\..\..\..\Views\StatisticsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5F0DDA01DF3413CEAB5E590AE5F7C3D2BF8D0BDC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// StatisticsView
    /// </summary>
    public partial class StatisticsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProcessedText;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessRateText;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgProcessingTimeText;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveUsersText;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TrendPeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas TrendChart;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas PieChart;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuUsageText;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CpuUsageBar;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryUsageText;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MemoryUsageBar;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskUsageText;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar DiskUsageBar;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Views\StatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView RecentActivitiesListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/statisticsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\StatisticsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalProcessedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SuccessRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AvgProcessingTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ActiveUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TrendPeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 142 "..\..\..\..\Views\StatisticsView.xaml"
            this.TrendPeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TrendPeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TrendChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 7:
            this.PieChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 8:
            this.CpuUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CpuUsageBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 10:
            this.MemoryUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MemoryUsageBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 12:
            this.DiskUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DiskUsageBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 14:
            this.RecentActivitiesListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 15:
            
            #line 371 "..\..\..\..\Views\StatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 383 "..\..\..\..\Views\StatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

