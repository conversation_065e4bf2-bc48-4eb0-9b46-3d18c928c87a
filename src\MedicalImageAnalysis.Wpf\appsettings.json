{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Application": {"Name": "医学影像解析系统", "Version": "1.0.0", "DataDirectory": "./data", "TempDirectory": "./temp", "LogDirectory": "./logs"}, "Api": {"BaseUrl": "http://localhost:5000", "Timeout": 30, "RetryCount": 3}, "Processing": {"MaxConcurrency": 2, "DefaultConfidenceThreshold": 0.5, "MaxFileSize": 524288000, "SupportedFormats": [".dcm", ".dicom", ".png", ".jpg", ".jpeg", ".bmp"]}, "Training": {"DefaultEpochs": 100, "DefaultBatchSize": 8, "DefaultLearningRate": 0.001, "ModelOutputDirectory": "./models", "CheckpointInterval": 10}, "UI": {"Theme": "Light", "PrimaryColor": "Blue", "SecondaryColor": "LightBlue", "AutoRefreshInterval": 30, "MaxRecentFiles": 10}}