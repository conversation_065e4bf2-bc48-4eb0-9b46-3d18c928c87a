<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.HomeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <Grid>
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="医学影像解析系统" 
                     FontSize="32" 
                     FontWeight="Bold"
                     HorizontalAlignment="Center"
                     Margin="0,0,0,16"/>
            <TextBlock Text="系统正在运行中..." 
                     FontSize="16"
                     HorizontalAlignment="Center"
                     Foreground="Green"/>
        </StackPanel>
    </Grid>
</UserControl>
