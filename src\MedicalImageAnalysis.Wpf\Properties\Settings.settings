<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="MedicalImageAnalysis.Wpf.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="DefaultDicomDirectory" Type="System.String" Scope="User">
      <Value Profile="(Default)">C:\DICOM</Value>
    </Setting>
    <Setting Name="DefaultOutputDirectory" Type="System.String" Scope="User">
      <Value Profile="(Default)">C:\Output</Value>
    </Setting>
    <Setting Name="DefaultModelPath" Type="System.String" Scope="User">
      <Value Profile="(Default)">models\default.pt</Value>
    </Setting>
    <Setting Name="AutoSaveEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="MaxRecentFiles" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">10</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
  </Settings>
</SettingsFile>
