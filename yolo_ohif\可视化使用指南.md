# 数据集可视化使用指南

本指南将帮助您快速验证切出来的图像和标签是否正确匹配。

## 🎯 快速开始

### 1. 最简单的方式 - 交互式脚本

```bash
python visualize_examples.py
```

运行后会出现菜单，选择相应选项：
- **选项1**: 快速检查数据集完整性
- **选项2**: 可视化数据集样本并保存图片
- **选项3**: 显示特定图像（弹出窗口）

### 2. 命令行方式

#### 检查数据集完整性
```bash
python visualize_dataset.py --check_only
```

这会显示：
- 各数据集分割的文件数量
- 图像和标签的匹配情况
- 不匹配的文件列表

#### 可视化样本并保存
```bash
# 可视化10个训练样本
python visualize_dataset.py --split train --num_samples 10 --save_dir ./可视化结果

# 可视化5个验证样本
python visualize_dataset.py --split val --num_samples 5 --save_dir ./可视化结果
```

### 3. Jupyter Notebook方式（推荐）

```bash
jupyter notebook dataset_visualization.ipynb
```

提供最丰富的可视化体验，包括：
- 交互式图像显示
- 数据集统计图表
- 类别分布分析

## 📊 可视化内容说明

### 图像显示
- **红色边界框**: 冈上肌撕裂区域
- **绿色边界框**: 正常区域（如果有）
- **标题信息**: 图像名称、尺寸、检测目标数量

### 标注信息
- **中心坐标**: YOLO格式的归一化坐标
- **尺寸**: 边界框的宽度和高度（归一化）
- **类别**: 撕裂或正常

## 🔍 验证要点

### 1. 数据完整性
- [ ] 图像文件和标签文件数量匹配
- [ ] 没有孤立的图像或标签文件
- [ ] 文件命名一致

### 2. 标注质量
- [ ] 边界框准确框选撕裂区域
- [ ] 边界框大小合适（不过大或过小）
- [ ] 没有明显的标注错误

### 3. 图像质量
- [ ] 图像清晰可见
- [ ] 尺寸统一（应为640x640）
- [ ] 对比度和亮度适中

### 4. 数据分布
- [ ] 训练集、验证集、测试集比例合理
- [ ] 撕裂和正常样本分布均衡
- [ ] 各层面的图像都有代表性

## 🚨 常见问题排查

### 问题1: 显示"无法加载图像"
**原因**: 图像文件路径错误或文件损坏
**解决**: 检查数据集路径设置，确认图像文件存在

### 问题2: 边界框位置不正确
**原因**: YOLO坐标转换错误或标注文件格式问题
**解决**: 检查标注文件格式，确认坐标值在0-1范围内

### 问题3: 没有显示边界框
**原因**: 标签文件为空或格式错误
**解决**: 检查对应的.txt文件内容

### 问题4: 图像和标签不匹配
**原因**: 文件名不对应或处理逻辑错误
**解决**: 重新运行数据处理脚本，确保层面匹配

## 💡 使用建议

1. **训练前必检**: 每次训练前都要可视化几个样本
2. **分批检查**: 不要一次可视化太多样本，建议5-10个
3. **重点关注**: 特别检查撕裂样本的标注质量
4. **保存结果**: 使用`--save_dir`参数保存可视化结果供后续参考
5. **记录问题**: 发现问题及时记录并修正

## 📁 输出文件说明

可视化结果会保存在指定目录中，文件命名格式：
- `train_sample_001_文件名.png`: 训练集样本
- `val_sample_001_文件名.png`: 验证集样本
- `test_sample_001_文件名.png`: 测试集样本

每个文件包含：
- 原始图像
- 叠加的边界框
- 类别标签
- 详细信息标题

## 🔧 自定义配置

如需修改可视化参数，可以编辑 `visualize_dataset.py` 中的设置：

```python
# 修改类别颜色
self.colors = {
    0: 'red',    # 撕裂用红色
    1: 'blue'    # 改为蓝色
}

# 修改图像显示大小
fig, ax = plt.subplots(1, 1, figsize=(15, 10))  # 增大显示尺寸
```

---

通过这些可视化工具，您可以确保数据处理的正确性，为后续的YOLO训练打下坚实基础！